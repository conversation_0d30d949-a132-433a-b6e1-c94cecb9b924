import json
import pandas as pd
import numpy as np
from collections import defaultdict

def load_all_signals_data():
    """Load all signals data for the specified dates"""
    dates = [20, 25, 26]
    all_signals = []
    
    for date in dates:
        try:
            filename = f"signals/signals_2025-07-{date:02d}.json"
            with open(filename, 'r') as f:
                signals = json.load(f)
                all_signals.extend(signals)
                print(f"✅ Loaded {len(signals)} signals from {filename}")
        except Exception as e:
            print(f"❌ Error loading {filename}: {e}")
    
    return all_signals

def comprehensive_analysis():
    """Perform comprehensive analysis of all signals"""
    
    # Load all signals
    all_signals = load_all_signals_data()
    
    if not all_signals:
        print("No signals data found!")
        return
    
    print(f"\n📊 Total signals loaded: {len(all_signals)}")
    
    # Convert to DataFrame
    df_data = []
    for signal in all_signals:
        row = {
            'date': signal['date'],
            'pair': signal['pair'],
            'direction': signal['direction'],
            'result': signal['result'],
            'price': signal['price'],
            'final_price': signal.get('final_price', signal['price']),
            'strategy': signal['strategy_used'],
            'confidence_score': signal.get('confidence_score', 0),
            'pattern_win_rate': signal.get('pattern_win_rate', 0),
            'pattern_count': signal.get('pattern_count', 0),
        }
        
        # Add indicators
        indicators = signal.get('indicators', {})
        for key, value in indicators.items():
            row[key] = value
            
        df_data.append(row)
    
    df = pd.DataFrame(df_data)
    
    print("\n" + "="*100)
    print("🎯 COMPREHENSIVE SIGNALS ANALYSIS - WHY SIGNALS WIN OR LOSE")
    print("="*100)
    
    # Basic statistics
    total_signals = len(df)
    wins = len(df[df['result'] == 'win'])
    losses = len(df[df['result'] == 'loss'])
    overall_win_rate = wins / total_signals * 100
    
    print(f"\n📈 OVERALL PERFORMANCE:")
    print(f"   Total Signals: {total_signals}")
    print(f"   Wins: {wins} ({overall_win_rate:.2f}%)")
    print(f"   Losses: {losses} ({100-overall_win_rate:.2f}%)")
    
    # Performance by date
    print(f"\n📅 PERFORMANCE BY DATE:")
    for date in sorted(df['date'].unique()):
        date_df = df[df['date'] == date]
        date_wins = len(date_df[date_df['result'] == 'win'])
        date_losses = len(date_df[date_df['result'] == 'loss'])
        date_total = len(date_df)
        date_win_rate = date_wins / date_total * 100
        print(f"   {date}: {date_wins:3d} wins, {date_losses:3d} losses ({date_win_rate:5.1f}% win rate) - Total: {date_total}")
    
    wins_df = df[df['result'] == 'win'].copy()
    losses_df = df[df['result'] == 'loss'].copy()
    
    # RSI Analysis
    print(f"\n🎯 RSI ANALYSIS - KEY FINDINGS:")
    print("-" * 70)
    
    rsi_ranges = [
        (0, 10, "Extremely Oversold (0-10)"),
        (10, 20, "Very Oversold (10-20)"),
        (20, 30, "Oversold (20-30)"),
        (30, 50, "Bearish (30-50)"),
        (50, 70, "Bullish (50-70)"),
        (70, 80, "Overbought (70-80)"),
        (80, 100, "Very Overbought (80-100)")
    ]
    
    best_rsi_conditions = []
    worst_rsi_conditions = []
    
    for min_rsi, max_rsi, label in rsi_ranges:
        win_count = len(wins_df[(wins_df['rsi'] >= min_rsi) & (wins_df['rsi'] < max_rsi)])
        loss_count = len(losses_df[(losses_df['rsi'] >= min_rsi) & (losses_df['rsi'] < max_rsi)])
        total = win_count + loss_count
        
        if total > 5:  # Only consider ranges with sufficient data
            win_rate = win_count / total * 100
            print(f"   {label:25}: {win_count:3d} wins, {loss_count:3d} losses → {win_rate:5.1f}% win rate")
            
            if win_rate > 55:
                best_rsi_conditions.append((label, win_rate, win_count, loss_count))
            elif win_rate < 45:
                worst_rsi_conditions.append((label, win_rate, win_count, loss_count))
    
    # MACD Analysis
    print(f"\n📈 MACD ANALYSIS - TREND INSIGHTS:")
    print("-" * 70)
    
    # Calculate MACD conditions
    wins_df['macd_bullish'] = wins_df['macd'] > wins_df['macd_signal']
    losses_df['macd_bullish'] = losses_df['macd'] > losses_df['macd_signal']
    
    macd_bullish_wins = len(wins_df[wins_df['macd_bullish']])
    macd_bullish_losses = len(losses_df[losses_df['macd_bullish']])
    macd_bearish_wins = len(wins_df[~wins_df['macd_bullish']])
    macd_bearish_losses = len(losses_df[~losses_df['macd_bullish']])
    
    if macd_bullish_wins + macd_bullish_losses > 0:
        bullish_win_rate = macd_bullish_wins / (macd_bullish_wins + macd_bullish_losses) * 100
        print(f"   MACD Bullish (MACD > Signal): {macd_bullish_wins:3d} wins, {macd_bullish_losses:3d} losses → {bullish_win_rate:5.1f}% win rate")
    
    if macd_bearish_wins + macd_bearish_losses > 0:
        bearish_win_rate = macd_bearish_wins / (macd_bearish_wins + macd_bearish_losses) * 100
        print(f"   MACD Bearish (MACD < Signal): {macd_bearish_wins:3d} wins, {macd_bearish_losses:3d} losses → {bearish_win_rate:5.1f}% win rate")
    
    # Direction Analysis
    print(f"\n🎯 DIRECTION ANALYSIS:")
    print("-" * 70)
    
    for direction in ['call', 'put']:
        dir_wins = len(wins_df[wins_df['direction'] == direction])
        dir_losses = len(losses_df[losses_df['direction'] == direction])
        dir_total = dir_wins + dir_losses
        
        if dir_total > 0:
            dir_win_rate = dir_wins / dir_total * 100
            print(f"   {direction.upper()} signals: {dir_wins:3d} wins, {dir_losses:3d} losses → {dir_win_rate:5.1f}% win rate")
    
    # Combined Analysis - Most Important Findings
    print(f"\n🔥 KEY INSIGHTS - WHAT MAKES SIGNALS WIN:")
    print("-" * 70)
    
    # Analyze winning combinations
    winning_insights = []
    
    # RSI + Direction combinations
    for direction in ['call', 'put']:
        for min_rsi, max_rsi, rsi_label in [(0, 15, "Very Low RSI"), (15, 30, "Low RSI")]:
            condition_wins = wins_df[(wins_df['direction'] == direction) & 
                                   (wins_df['rsi'] >= min_rsi) & 
                                   (wins_df['rsi'] < max_rsi)]
            condition_losses = losses_df[(losses_df['direction'] == direction) & 
                                       (losses_df['rsi'] >= min_rsi) & 
                                       (losses_df['rsi'] < max_rsi)]
            
            total = len(condition_wins) + len(condition_losses)
            if total >= 10:
                win_rate = len(condition_wins) / total * 100
                winning_insights.append({
                    'condition': f"{direction.upper()} + {rsi_label}",
                    'wins': len(condition_wins),
                    'losses': len(condition_losses),
                    'win_rate': win_rate,
                    'total': total
                })
    
    # Sort by win rate and show top insights
    winning_insights.sort(key=lambda x: x['win_rate'], reverse=True)
    
    for insight in winning_insights[:5]:
        print(f"   ✅ {insight['condition']:20}: {insight['wins']:3d}W/{insight['losses']:3d}L → {insight['win_rate']:5.1f}% win rate")
    
    print(f"\n🚨 KEY INSIGHTS - WHAT MAKES SIGNALS LOSE:")
    print("-" * 70)
    
    # Find losing patterns
    losing_insights = []
    for insight in winning_insights:
        if insight['win_rate'] < 45:
            losing_insights.append(insight)
    
    losing_insights.sort(key=lambda x: x['win_rate'])
    
    for insight in losing_insights[:5]:
        loss_rate = 100 - insight['win_rate']
        print(f"   ❌ {insight['condition']:20}: {insight['wins']:3d}W/{insight['losses']:3d}L → {loss_rate:5.1f}% loss rate")
    
    # Summary of key findings
    print(f"\n" + "="*100)
    print("📋 SUMMARY - ACTIONABLE INSIGHTS")
    print("="*100)
    
    print(f"\n🎯 BEST CONDITIONS FOR WINNING SIGNALS:")
    if best_rsi_conditions:
        for condition, win_rate, wins, losses in best_rsi_conditions:
            print(f"   • {condition}: {win_rate:.1f}% win rate ({wins} wins, {losses} losses)")
    
    print(f"\n⚠️  CONDITIONS TO AVOID (HIGH LOSS RATE):")
    if worst_rsi_conditions:
        for condition, win_rate, wins, losses in worst_rsi_conditions:
            loss_rate = 100 - win_rate
            print(f"   • {condition}: {loss_rate:.1f}% loss rate ({wins} wins, {losses} losses)")
    
    # Calculate average indicator values for wins vs losses
    print(f"\n📊 AVERAGE INDICATOR VALUES:")
    print(f"   WINNING SIGNALS:")
    print(f"     • Average RSI: {wins_df['rsi'].mean():.2f}")
    print(f"     • Average MACD: {wins_df['macd'].mean():.6f}")
    print(f"     • Average Confidence: {wins_df['confidence_score'].mean():.3f}")
    
    print(f"   LOSING SIGNALS:")
    print(f"     • Average RSI: {losses_df['rsi'].mean():.2f}")
    print(f"     • Average MACD: {losses_df['macd'].mean():.6f}")
    print(f"     • Average Confidence: {losses_df['confidence_score'].mean():.3f}")
    
    print(f"\n✅ Analysis complete! Key patterns identified for signal optimization.")

if __name__ == "__main__":
    comprehensive_analysis()
