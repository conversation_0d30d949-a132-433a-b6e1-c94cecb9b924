import json
import pandas as pd
import numpy as np
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

def load_signals_data(dates):
    """Load signals data for specified dates"""
    all_signals = []
    
    for date in dates:
        try:
            filename = f"signals/signals_2025-07-{date:02d}.json"
            with open(filename, 'r') as f:
                signals = json.load(f)
                all_signals.extend(signals)
            print(f"Loaded {len(signals)} signals from {filename}")
        except FileNotFoundError:
            print(f"File not found: signals_2025-07-{date:02d}.json")
        except Exception as e:
            print(f"Error loading {filename}: {e}")
    
    return all_signals

def analyze_signal_patterns(signals):
    """Analyze patterns in winning vs losing signals"""
    
    # Convert to DataFrame for easier analysis
    df_data = []
    for signal in signals:
        row = {
            'date': signal['date'],
            'pair': signal['pair'],
            'direction': signal['direction'],
            'result': signal['result'],
            'price': signal['price'],
            'final_price': signal.get('final_price', signal['price']),
            'strategy': signal['strategy_used'],
            'confidence_score': signal.get('confidence_score', 0),
            'pattern_win_rate': signal.get('pattern_win_rate', 0),
            'pattern_count': signal.get('pattern_count', 0),
            'timeframe': signal.get('timeframe', 'M1')
        }
        
        # Add indicators
        indicators = signal.get('indicators', {})
        for key, value in indicators.items():
            row[f'ind_{key}'] = value
            
        df_data.append(row)
    
    df = pd.DataFrame(df_data)
    return df

def detailed_analysis(df):
    """Perform detailed analysis of winning vs losing signals"""
    
    print("="*80)
    print("COMPREHENSIVE SIGNALS ANALYSIS")
    print("="*80)
    
    # Basic statistics
    total_signals = len(df)
    wins = len(df[df['result'] == 'win'])
    losses = len(df[df['result'] == 'loss'])
    win_rate = wins / total_signals * 100
    
    print(f"\nOVERALL STATISTICS:")
    print(f"Total Signals: {total_signals}")
    print(f"Wins: {wins} ({win_rate:.2f}%)")
    print(f"Losses: {losses} ({100-win_rate:.2f}%)")
    
    # Analysis by date
    print(f"\nRESULTS BY DATE:")
    date_analysis = df.groupby('date')['result'].value_counts().unstack(fill_value=0)
    for date in date_analysis.index:
        date_wins = date_analysis.loc[date, 'win'] if 'win' in date_analysis.columns else 0
        date_losses = date_analysis.loc[date, 'loss'] if 'loss' in date_analysis.columns else 0
        date_total = date_wins + date_losses
        date_win_rate = date_wins / date_total * 100 if date_total > 0 else 0
        print(f"  {date}: {date_wins} wins, {date_losses} losses ({date_win_rate:.2f}% win rate)")
    
    # Indicator analysis for wins vs losses
    print(f"\nINDICATOR ANALYSIS - WINNING SIGNALS:")
    print("-" * 50)
    
    wins_df = df[df['result'] == 'win']
    losses_df = df[df['result'] == 'loss']
    
    indicator_cols = [col for col in df.columns if col.startswith('ind_')]
    
    for col in indicator_cols:
        if col in df.columns and df[col].notna().any():
            indicator_name = col.replace('ind_', '').upper()
            
            win_values = wins_df[col].dropna()
            loss_values = losses_df[col].dropna()
            
            if len(win_values) > 0 and len(loss_values) > 0:
                print(f"\n{indicator_name}:")
                print(f"  Winning signals - Mean: {win_values.mean():.6f}, Median: {win_values.median():.6f}")
                print(f"  Losing signals - Mean: {loss_values.mean():.6f}, Median: {loss_values.median():.6f}")
                print(f"  Range for wins: {win_values.min():.6f} to {win_values.max():.6f}")
                print(f"  Range for losses: {loss_values.min():.6f} to {loss_values.max():.6f}")
    
    return wins_df, losses_df

def find_winning_patterns(wins_df, losses_df):
    """Find specific patterns that lead to wins vs losses"""
    
    print(f"\n" + "="*80)
    print("PATTERN ANALYSIS - WHAT MAKES SIGNALS WIN OR LOSE")
    print("="*80)
    
    # RSI analysis
    if 'ind_rsi' in wins_df.columns:
        print(f"\nRSI ANALYSIS:")
        print("-" * 30)
        
        # RSI ranges for wins
        win_rsi = wins_df['ind_rsi'].dropna()
        loss_rsi = losses_df['ind_rsi'].dropna()
        
        if len(win_rsi) > 0:
            print(f"RSI patterns for WINNING signals:")
            print(f"  Oversold (RSI < 30): {len(win_rsi[win_rsi < 30])} wins")
            print(f"  Neutral (30 ≤ RSI ≤ 70): {len(win_rsi[(win_rsi >= 30) & (win_rsi <= 70)])} wins")
            print(f"  Overbought (RSI > 70): {len(win_rsi[win_rsi > 70])} wins")
            
        if len(loss_rsi) > 0:
            print(f"RSI patterns for LOSING signals:")
            print(f"  Oversold (RSI < 30): {len(loss_rsi[loss_rsi < 30])} losses")
            print(f"  Neutral (30 ≤ RSI ≤ 70): {len(loss_rsi[(loss_rsi >= 30) & (loss_rsi <= 70)])} losses")
            print(f"  Overbought (RSI > 70): {len(loss_rsi[loss_rsi > 70])} losses")
    
    # MACD analysis
    if 'ind_macd' in wins_df.columns and 'ind_macd_signal' in wins_df.columns:
        print(f"\nMACD ANALYSIS:")
        print("-" * 30)
        
        # MACD crossover analysis
        wins_macd_above = wins_df[wins_df['ind_macd'] > wins_df['ind_macd_signal']]
        wins_macd_below = wins_df[wins_df['ind_macd'] <= wins_df['ind_macd_signal']]
        
        losses_macd_above = losses_df[losses_df['ind_macd'] > losses_df['ind_macd_signal']]
        losses_macd_below = losses_df[losses_df['ind_macd'] <= losses_df['ind_macd_signal']]
        
        print(f"MACD above signal line:")
        print(f"  Wins: {len(wins_macd_above)}, Losses: {len(losses_macd_above)}")
        print(f"MACD below signal line:")
        print(f"  Wins: {len(wins_macd_below)}, Losses: {len(losses_macd_below)}")
    
    # Direction analysis
    print(f"\nDIRECTION ANALYSIS:")
    print("-" * 30)
    
    direction_wins = wins_df['direction'].value_counts()
    direction_losses = losses_df['direction'].value_counts()
    
    for direction in ['call', 'put']:
        wins_count = direction_wins.get(direction, 0)
        losses_count = direction_losses.get(direction, 0)
        total_direction = wins_count + losses_count
        win_rate = wins_count / total_direction * 100 if total_direction > 0 else 0
        print(f"{direction.upper()} signals: {wins_count} wins, {losses_count} losses ({win_rate:.2f}% win rate)")

def main():
    """Main analysis function"""
    
    # Load data for specified dates
    dates = [20, 25, 26]
    signals = load_signals_data(dates)
    
    if not signals:
        print("No signals data found!")
        return
    
    # Convert to DataFrame and analyze
    df = analyze_signal_patterns(signals)
    
    # Perform detailed analysis
    wins_df, losses_df = detailed_analysis(df)
    
    # Find specific patterns
    find_winning_patterns(wins_df, losses_df)
    
    # Save detailed results
    print(f"\nSaving detailed analysis to CSV files...")
    wins_df.to_csv('winning_signals_analysis.csv', index=False)
    losses_df.to_csv('losing_signals_analysis.csv', index=False)
    df.to_csv('all_signals_analysis.csv', index=False)
    
    print(f"\nAnalysis complete! Check the generated CSV files for detailed data.")

if __name__ == "__main__":
    main()
