#!/usr/bin/env python3
"""
Test Enhanced Echo Sniper Strategy
Validate the new filtering system against historical data
"""

import json
import pandas as pd
import numpy as np
import sys
import os

# Add the Train Bot directory to the path
sys.path.append('Train Bot')

from strategy_engine import StrategyEngine
from config import ECHO_SNIPER_CONFIG, TRADING_CONFIG
from utils import add_technical_indicators, print_colored

def load_test_signals():
    """Load signals from the analysis dates for testing"""
    dates = [20, 25, 26]
    all_signals = []
    
    for date in dates:
        try:
            filename = f"signals/signals_2025-07-{date:02d}.json"
            with open(filename, 'r') as f:
                signals = json.load(f)
                all_signals.extend(signals)
                print(f"✅ Loaded {len(signals)} signals from {filename}")
        except Exception as e:
            print(f"❌ Error loading {filename}: {e}")
    
    return all_signals

def create_mock_dataframe(signal_data):
    """Create a mock DataFrame from signal data for testing"""
    try:
        # Create mock candle data
        base_price = signal_data['price']
        
        # Generate 120 mock candles (enough for analysis)
        candles = []
        for i in range(120):
            # Create realistic price movement
            price_change = np.random.normal(0, 0.0001)  # Small random changes
            current_price = base_price + price_change * i
            
            candle = {
                'open': current_price,
                'high': current_price + abs(np.random.normal(0, 0.00005)),
                'low': current_price - abs(np.random.normal(0, 0.00005)),
                'close': current_price + np.random.normal(0, 0.00002),
                'volume': np.random.randint(100, 1000)
            }
            candles.append(candle)
        
        df = pd.DataFrame(candles)
        
        # Add the actual indicators from the signal
        indicators = signal_data.get('indicators', {})
        
        # Set the last candle to have the actual indicator values
        df = add_technical_indicators(df)
        
        # Override with actual values for the last candle
        if 'rsi' in indicators:
            df.loc[df.index[-1], 'rsi'] = indicators['rsi']
        if 'macd' in indicators:
            df.loc[df.index[-1], 'macd'] = indicators['macd']
        if 'macd_signal' in indicators:
            df.loc[df.index[-1], 'macd_signal'] = indicators['macd_signal']
        if 'sma_20' in indicators:
            df.loc[df.index[-1], 'sma_20'] = indicators['sma_20']
        if 'ema_12' in indicators:
            df.loc[df.index[-1], 'ema_12'] = indicators['ema_12']
        if 'ema_26' in indicators:
            df.loc[df.index[-1], 'ema_26'] = indicators['ema_26']
        
        return df
        
    except Exception as e:
        print(f"❌ Error creating mock DataFrame: {e}")
        return None

def test_enhanced_strategy():
    """Test the enhanced strategy against historical signals"""
    
    print("="*100)
    print("🧪 TESTING ENHANCED ECHO SNIPER STRATEGY")
    print("="*100)
    
    # Load historical signals
    signals = load_test_signals()
    if not signals:
        print("❌ No signals loaded for testing")
        return
    
    # Initialize enhanced strategy engine
    strategy_engine = StrategyEngine()
    
    # Test counters
    total_tested = 0
    signals_generated = 0
    signals_rejected = 0
    wins_generated = 0
    losses_generated = 0
    
    # Filter analysis
    rsi_rejections = 0
    macd_rejections = 0
    confidence_rejections = 0
    direction_rejections = 0
    
    print(f"\n📊 Testing {len(signals)} historical signals...")
    
    for i, signal_data in enumerate(signals):
        try:
            # Create mock DataFrame with actual indicator values
            df = create_mock_dataframe(signal_data)
            if df is None:
                continue
            
            total_tested += 1
            
            # Test the enhanced strategy
            result = strategy_engine.evaluate_all_strategies(df)
            
            # Check if signal was generated
            if result['signal'] != 'HOLD':
                signals_generated += 1
                
                # Check if this would have been a win or loss
                actual_result = signal_data['result']
                if actual_result == 'win':
                    wins_generated += 1
                else:
                    losses_generated += 1
                    
                print(f"✅ Signal #{i+1}: {result['signal']} | Confidence: {result['confidence']:.3f} | Actual: {actual_result.upper()}")
            else:
                signals_rejected += 1
                
                # Analyze rejection reasons (would need to modify strategy to return reasons)
                indicators = signal_data.get('indicators', {})
                rsi = indicators.get('rsi', 50)
                macd = indicators.get('macd', 0)
                macd_signal = indicators.get('macd_signal', 0)
                
                # Count rejection reasons
                if 50 <= rsi <= 70:
                    rsi_rejections += 1
                elif macd >= macd_signal:
                    macd_rejections += 1
                else:
                    confidence_rejections += 1
            
            # Progress indicator
            if (i + 1) % 100 == 0:
                print(f"📈 Processed {i+1}/{len(signals)} signals...")
                
        except Exception as e:
            print(f"❌ Error testing signal #{i+1}: {e}")
            continue
    
    # Calculate results
    if signals_generated > 0:
        enhanced_win_rate = wins_generated / signals_generated * 100
    else:
        enhanced_win_rate = 0
    
    original_wins = len([s for s in signals if s['result'] == 'win'])
    original_win_rate = original_wins / len(signals) * 100
    
    # Display results
    print("\n" + "="*100)
    print("📊 ENHANCED STRATEGY TEST RESULTS")
    print("="*100)
    
    print(f"\n📈 SIGNAL GENERATION:")
    print(f"   Total Signals Tested: {total_tested}")
    print(f"   Signals Generated: {signals_generated}")
    print(f"   Signals Rejected: {signals_rejected}")
    print(f"   Generation Rate: {signals_generated/total_tested*100:.1f}%")
    
    print(f"\n🎯 WIN RATE COMPARISON:")
    print(f"   Original Win Rate: {original_win_rate:.1f}% ({original_wins}/{len(signals)})")
    print(f"   Enhanced Win Rate: {enhanced_win_rate:.1f}% ({wins_generated}/{signals_generated})")
    print(f"   Improvement: {enhanced_win_rate - original_win_rate:+.1f} percentage points")
    
    print(f"\n🚫 REJECTION ANALYSIS:")
    print(f"   RSI Filter Rejections: {rsi_rejections} (50-70 range)")
    print(f"   MACD Filter Rejections: {macd_rejections} (bullish MACD)")
    print(f"   Confidence Rejections: {confidence_rejections} (low confidence)")
    print(f"   Direction Rejections: {direction_rejections} (direction filters)")
    
    print(f"\n✅ QUALITY ASSESSMENT:")
    if enhanced_win_rate >= 70:
        print(f"   🎉 TARGET ACHIEVED: {enhanced_win_rate:.1f}% win rate >= 70% target!")
    elif enhanced_win_rate >= 60:
        print(f"   📈 GOOD PROGRESS: {enhanced_win_rate:.1f}% win rate (close to 70% target)")
    else:
        print(f"   ⚠️  NEEDS IMPROVEMENT: {enhanced_win_rate:.1f}% win rate below 60%")
    
    # Recommendations
    print(f"\n🔧 RECOMMENDATIONS:")
    if signals_generated < total_tested * 0.1:
        print(f"   • Strategy is very selective ({signals_generated/total_tested*100:.1f}% generation rate)")
        print(f"   • Consider slightly relaxing filters if win rate is high")
    elif signals_generated > total_tested * 0.3:
        print(f"   • Strategy generates many signals ({signals_generated/total_tested*100:.1f}% generation rate)")
        print(f"   • Consider tightening filters for higher quality")
    
    if enhanced_win_rate < 70:
        print(f"   • Win rate below target - consider:")
        print(f"     - Increasing minimum win rate threshold")
        print(f"     - Adding more restrictive RSI filters")
        print(f"     - Requiring higher confidence scores")

if __name__ == "__main__":
    test_enhanced_strategy()
