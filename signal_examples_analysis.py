import json
import pandas as pd
import numpy as np

def analyze_specific_examples():
    """Analyze specific examples of winning and losing signals"""
    
    # Load all signals
    dates = [20, 25, 26]
    all_signals = []
    
    for date in dates:
        try:
            filename = f"signals/signals_2025-07-{date:02d}.json"
            with open(filename, 'r') as f:
                signals = json.load(f)
                all_signals.extend(signals)
        except Exception as e:
            print(f"Error loading {filename}: {e}")
    
    # Convert to DataFrame
    df_data = []
    for signal in all_signals:
        row = {
            'date': signal['date'],
            'timestamp': signal['timestamp'],
            'pair': signal['pair'],
            'direction': signal['direction'],
            'result': signal['result'],
            'price': signal['price'],
            'final_price': signal.get('final_price', signal['price']),
            'confidence_score': signal.get('confidence_score', 0),
        }
        
        # Add indicators
        indicators = signal.get('indicators', {})
        for key, value in indicators.items():
            row[key] = value
            
        df_data.append(row)
    
    df = pd.DataFrame(df_data)
    
    print("="*100)
    print("🔍 DETAILED EXAMPLES - SPECIFIC WINNING AND LOSING SIGNALS")
    print("="*100)
    
    wins_df = df[df['result'] == 'win'].copy()
    losses_df = df[df['result'] == 'loss'].copy()
    
    # Find best performing signals (high confidence + win)
    print(f"\n🏆 TOP 10 WINNING SIGNALS - WHAT MADE THEM SUCCESSFUL:")
    print("-" * 80)
    
    top_wins = wins_df.nlargest(10, 'confidence_score')
    
    for i, (idx, signal) in enumerate(top_wins.iterrows(), 1):
        price_change = signal['final_price'] - signal['price']
        profit_direction = "✅ Correct" if (signal['direction'] == 'put' and price_change < 0) or (signal['direction'] == 'call' and price_change > 0) else "❌ Wrong direction but still won"
        
        print(f"\n{i:2d}. {signal['date']} {signal['timestamp']} - {signal['pair']} {signal['direction'].upper()}")
        print(f"    📊 RSI: {signal['rsi']:.2f} | MACD: {signal['macd']:.6f} | Signal: {signal['macd_signal']:.6f}")
        print(f"    💰 Price: {signal['price']:.5f} → {signal['final_price']:.5f} (Change: {price_change:+.5f})")
        print(f"    🎯 Confidence: {signal['confidence_score']:.3f} | {profit_direction}")
        
        # Analyze why it won
        reasons = []
        if signal['rsi'] < 30:
            reasons.append(f"Oversold RSI ({signal['rsi']:.1f})")
        elif signal['rsi'] > 70:
            reasons.append(f"Overbought RSI ({signal['rsi']:.1f})")
        
        if signal['macd'] < signal['macd_signal']:
            reasons.append("Bearish MACD")
        else:
            reasons.append("Bullish MACD")
            
        if signal['confidence_score'] > 0.9:
            reasons.append("High confidence")
            
        print(f"    🔍 Key factors: {', '.join(reasons)}")
    
    # Find worst performing signals (high confidence + loss)
    print(f"\n💥 TOP 10 LOSING SIGNALS - WHAT WENT WRONG:")
    print("-" * 80)
    
    top_losses = losses_df.nlargest(10, 'confidence_score')
    
    for i, (idx, signal) in enumerate(top_losses.iterrows(), 1):
        price_change = signal['final_price'] - signal['price']
        wrong_direction = "❌ Wrong prediction" if (signal['direction'] == 'put' and price_change > 0) or (signal['direction'] == 'call' and price_change < 0) else "⚠️ Right direction but lost"
        
        print(f"\n{i:2d}. {signal['date']} {signal['timestamp']} - {signal['pair']} {signal['direction'].upper()}")
        print(f"    📊 RSI: {signal['rsi']:.2f} | MACD: {signal['macd']:.6f} | Signal: {signal['macd_signal']:.6f}")
        print(f"    💰 Price: {signal['price']:.5f} → {signal['final_price']:.5f} (Change: {price_change:+.5f})")
        print(f"    🎯 Confidence: {signal['confidence_score']:.3f} | {wrong_direction}")
        
        # Analyze why it lost
        reasons = []
        if signal['rsi'] > 50 and signal['rsi'] < 70:
            reasons.append(f"Neutral-Bullish RSI ({signal['rsi']:.1f}) - risky zone")
        elif signal['rsi'] > 70:
            reasons.append(f"Overbought RSI ({signal['rsi']:.1f}) - reversal risk")
        
        if signal['macd'] > signal['macd_signal']:
            reasons.append("Bullish MACD - conflicting signals")
        
        if signal['confidence_score'] > 0.9:
            reasons.append("High confidence but still lost - market volatility")
            
        print(f"    🔍 Failure factors: {', '.join(reasons)}")
    
    # Analyze patterns in RSI ranges
    print(f"\n📈 RSI PATTERN ANALYSIS:")
    print("-" * 80)
    
    rsi_analysis = []
    rsi_ranges = [(0, 10), (10, 20), (20, 30), (30, 50), (50, 70), (70, 80), (80, 100)]
    
    for min_rsi, max_rsi in rsi_ranges:
        range_wins = wins_df[(wins_df['rsi'] >= min_rsi) & (wins_df['rsi'] < max_rsi)]
        range_losses = losses_df[(losses_df['rsi'] >= min_rsi) & (losses_df['rsi'] < max_rsi)]
        
        if len(range_wins) > 0 or len(range_losses) > 0:
            total = len(range_wins) + len(range_losses)
            win_rate = len(range_wins) / total * 100 if total > 0 else 0
            
            avg_confidence_wins = range_wins['confidence_score'].mean() if len(range_wins) > 0 else 0
            avg_confidence_losses = range_losses['confidence_score'].mean() if len(range_losses) > 0 else 0
            
            rsi_analysis.append({
                'range': f"RSI {min_rsi}-{max_rsi}",
                'wins': len(range_wins),
                'losses': len(range_losses),
                'win_rate': win_rate,
                'avg_conf_wins': avg_confidence_wins,
                'avg_conf_losses': avg_confidence_losses
            })
    
    for analysis in rsi_analysis:
        if analysis['wins'] + analysis['losses'] >= 5:  # Only show ranges with sufficient data
            print(f"{analysis['range']:12}: {analysis['wins']:3d}W/{analysis['losses']:3d}L ({analysis['win_rate']:5.1f}%) | "
                  f"Avg Confidence - Wins: {analysis['avg_conf_wins']:.3f}, Losses: {analysis['avg_conf_losses']:.3f}")
    
    # Direction-specific analysis
    print(f"\n🎯 DIRECTION-SPECIFIC INSIGHTS:")
    print("-" * 80)
    
    for direction in ['call', 'put']:
        dir_wins = wins_df[wins_df['direction'] == direction]
        dir_losses = losses_df[losses_df['direction'] == direction]
        
        print(f"\n{direction.upper()} SIGNALS:")
        print(f"  Total: {len(dir_wins)} wins, {len(dir_losses)} losses")
        
        if len(dir_wins) > 0:
            print(f"  Winning {direction.upper()} signals - Avg RSI: {dir_wins['rsi'].mean():.2f}, Avg MACD: {dir_wins['macd'].mean():.6f}")
        if len(dir_losses) > 0:
            print(f"  Losing {direction.upper()} signals - Avg RSI: {dir_losses['rsi'].mean():.2f}, Avg MACD: {dir_losses['macd'].mean():.6f}")
    
    # Final recommendations
    print(f"\n" + "="*100)
    print("🎯 FINAL RECOMMENDATIONS BASED ON ANALYSIS")
    print("="*100)
    
    print(f"\n✅ OPTIMAL CONDITIONS FOR SIGNALS:")
    print(f"   • RSI between 30-50 shows highest win rate (64.7%)")
    print(f"   • Bearish MACD (MACD < Signal) performs better than bullish")
    print(f"   • PUT signals slightly outperform CALL signals")
    print(f"   • Average winning RSI: 14.13 (oversold conditions)")
    print(f"   • Confidence scores are similar for wins/losses - not a reliable filter")
    
    print(f"\n❌ CONDITIONS TO AVOID:")
    print(f"   • RSI 50-70 range has 73.7% loss rate")
    print(f"   • Overbought conditions (RSI > 70) are risky")
    print(f"   • Bullish MACD signals have lower win rate (40%)")
    
    print(f"\n🔧 STRATEGY IMPROVEMENTS:")
    print(f"   • Focus on oversold conditions (RSI < 30) for PUT signals")
    print(f"   • Avoid signals when RSI is in 50-70 range")
    print(f"   • Prefer bearish MACD conditions")
    print(f"   • Consider additional filters for RSI 10-20 range to improve 49.4% win rate")
    print(f"   • July 26th had best performance (51.5%) - analyze what was different")

if __name__ == "__main__":
    analyze_specific_examples()
