#!/usr/bin/env python3
"""
Echo Sniper Strategy Engine for Trading Bot
Pattern-based trading using historical candle pattern analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime
from config import STRATEGY_CONFIG, TRADING_CONFIG, ECHO_SNIPER_CONFIG
from utils import print_colored, format_price

class StrategyEngine:
    def __init__(self, echo_sniper_config=None):
        """Initialize the strategy engine with Echo Sniper configuration"""
        self.strategies = STRATEGY_CONFIG
        self.echo_sniper_config = echo_sniper_config or ECHO_SNIPER_CONFIG.copy()

    def get_candle_direction(self, candle):
        """Determine candle direction: 'green' or 'red' (no doji for simplicity)"""
        # Use a small threshold to avoid doji classification
        price_diff = candle['close'] - candle['open']

        if price_diff > 0:
            return 'green'
        else:
            return 'red'  # Treat doji as red for pattern consistency
    
    def detect_pattern(self, df, pattern_length):
        """Detect pattern from last N closed candles (excluding current running candle)"""
        try:
            # Use only closed candles (exclude current running candle)
            closed_candles = df.iloc[:-1]  # Exclude last (current) candle
            
            if len(closed_candles) < pattern_length:
                return None, "Insufficient closed candles for pattern detection"
            
            # Get last N closed candles for pattern
            pattern_candles = closed_candles.tail(pattern_length)
            pattern = []
            
            for _, candle in pattern_candles.iterrows():
                direction = self.get_candle_direction(candle)
                pattern.append(direction)
            
            return pattern, None
            
        except Exception as e:
            return None, f"Error detecting pattern: {str(e)}"
    
    def find_historical_patterns(self, df, target_pattern, historical_candles):
        """Find all occurrences of target pattern in historical data"""
        try:
            matches = []
            pattern_length = len(target_pattern)
            
            # Search through historical data (excluding recent candles used for current pattern)
            search_data = df.iloc[:-pattern_length-1]  # Exclude current pattern and running candle
            
            if len(search_data) < historical_candles:
                historical_candles = len(search_data)
            
            # Limit search to specified historical candles
            if historical_candles > 0:
                search_data = search_data.tail(historical_candles)
            
            # Search for pattern matches
            for i in range(len(search_data) - pattern_length + 1):
                # Extract pattern at current position
                pattern_candles = search_data.iloc[i:i + pattern_length]
                current_pattern = []
                
                for _, candle in pattern_candles.iterrows():
                    direction = self.get_candle_direction(candle)
                    current_pattern.append(direction)
                
                # Check if pattern matches
                if current_pattern == target_pattern:
                    # Get the next candle after the pattern (if exists)
                    next_index = i + pattern_length
                    if next_index < len(search_data):
                        next_candle = search_data.iloc[next_index]
                        next_direction = self.get_candle_direction(next_candle)
                        matches.append(next_direction)
            
            return matches, None
            
        except Exception as e:
            return [], f"Error finding historical patterns: {str(e)}"
    
    def calculate_pattern_statistics(self, matches):
        """Calculate win rate and statistics from pattern matches"""
        try:
            if not matches:
                return 0.0, 0, 0, 0, None
            
            total_matches = len(matches)
            green_wins = matches.count('green')
            red_wins = matches.count('red')
            
            # Determine expected direction based on majority
            if green_wins > red_wins:
                expected_direction = 'green'
                win_rate = green_wins / total_matches
            elif red_wins > green_wins:
                expected_direction = 'red'
                win_rate = red_wins / total_matches
            else:
                # Equal or no clear winner
                expected_direction = None
                win_rate = 0.5
            
            return win_rate, total_matches, green_wins, red_wins, expected_direction
            
        except Exception as e:
            print_colored(f"❌ Error calculating pattern statistics: {str(e)}", "ERROR")
            return 0.0, 0, 0, 0, None
    
    def check_pattern_completion(self, df, expected_direction):
        """Check if current candle already moved in expected direction"""
        try:
            current_candle = df.iloc[-1]
            current_direction = self.get_candle_direction(current_candle)
            
            # If current candle already moved as expected, pattern is completed
            if current_direction == expected_direction:
                return True, f"Pattern already completed - current candle is {current_direction}"
            
            return False, "Pattern not yet completed"
            
        except Exception as e:
            return True, f"Error checking pattern completion: {str(e)}"
    
    def calculate_confidence_score(self, win_rate, total_matches):
        """Calculate confidence score based on win rate and number of occurrences"""
        try:
            thresholds = self.echo_sniper_config['confidence_thresholds']

            # High confidence
            if (win_rate >= thresholds['high']['min_win_rate'] and
                total_matches >= thresholds['high']['min_occurrences']):
                return 'high', min(0.90, 0.70 + (win_rate - 0.75) * 0.8)

            # Medium confidence
            elif (win_rate >= thresholds['medium']['min_win_rate'] and
                  total_matches >= thresholds['medium']['min_occurrences']):
                return 'medium', min(0.80, 0.60 + (win_rate - 0.65) * 0.8)

            # Low confidence
            elif (win_rate >= thresholds['low']['min_win_rate'] and
                  total_matches >= thresholds['low']['min_occurrences']):
                return 'low', min(0.70, 0.50 + (win_rate - 0.55) * 0.8)

            # Below threshold
            else:
                return 'none', 0.0

        except Exception as e:
            print_colored(f"❌ Error calculating confidence: {str(e)}", "ERROR")
            return 'none', 0.0

    def apply_advanced_filters(self, df):
        """
        Apply BALANCED-STRICT advanced filters for 70%+ win rate
        Returns: {'passed': bool, 'reason': str, 'indicators': dict}
        """
        try:
            from utils import add_technical_indicators

            # Add technical indicators to dataframe
            df_with_indicators = add_technical_indicators(df.copy())
            current_candle = df_with_indicators.iloc[-1]

            # Get current indicator values
            current_rsi = current_candle['rsi']
            current_macd = current_candle['macd']
            current_macd_signal = current_candle['macd_signal']

            indicators = {
                'rsi': current_rsi,
                'macd': current_macd,
                'macd_signal': current_macd_signal,
                'macd_bearish': current_macd < current_macd_signal,
                'macd_strength': abs(current_macd - current_macd_signal),
                'quality_score': 0.0
            }

            config = self.echo_sniper_config.get('advanced_filters', {})

            # CRITICAL RSI Filter - Avoid the deadly 50-70 range
            if config.get('enable_rsi_filter', True):
                rsi_config = config.get('rsi_filters', {})
                critical_avoid = rsi_config.get('critical_avoid_range', [50, 70])

                # ABSOLUTE REJECTION for deadly range
                if critical_avoid[0] <= current_rsi <= critical_avoid[1]:
                    return {
                        'passed': False,
                        'reason': f'RSI {current_rsi:.1f} in DEADLY range {critical_avoid[0]}-{critical_avoid[1]} (73.7% loss rate) - ABSOLUTE REJECTION',
                        'indicators': indicators
                    }

                # Check if RSI is in optimal ranges
                optimal_ranges = rsi_config.get('optimal_ranges', [[30, 50], [10, 30]])
                rsi_quality_score = 0.0

                for range_min, range_max in optimal_ranges:
                    if range_min <= current_rsi <= range_max:
                        if range_min == 30 and range_max == 50:
                            rsi_quality_score = 1.0  # Best range
                        elif range_min == 10 and range_max == 30:
                            rsi_quality_score = 0.7  # Good range
                        break

                # Handle extreme caution ranges
                extreme_ranges = rsi_config.get('extreme_caution_ranges', [[0, 10], [70, 100]])
                for range_min, range_max in extreme_ranges:
                    if range_min <= current_rsi <= range_max:
                        rsi_quality_score = 0.3  # Caution range - very low quality
                        break

                indicators['quality_score'] = rsi_quality_score

                # Reject if RSI has no quality score (not in any acceptable range)
                if rsi_quality_score == 0.0:
                    return {
                        'passed': False,
                        'reason': f'RSI {current_rsi:.1f} not in any acceptable range',
                        'indicators': indicators
                    }

            # MACD Filter - Prefer bearish but not ultra-strict
            if config.get('enable_macd_filter', True):
                macd_config = config.get('macd_filters', {})

                # Prefer bearish MACD
                if macd_config.get('prefer_bearish_macd', True):
                    if current_macd >= current_macd_signal:
                        # Check if it's strongly bullish (reject) vs weakly bullish (caution)
                        if macd_config.get('reject_strong_bullish', True):
                            macd_strength = current_macd - current_macd_signal
                            if macd_strength > 0.005:  # Strong bullish
                                return {
                                    'passed': False,
                                    'reason': f'Strong bullish MACD ({current_macd:.6f} >> {current_macd_signal:.6f}) - high risk',
                                    'indicators': indicators
                                }

                        # Weak bullish - reduce quality but don't reject
                        indicators['quality_score'] *= 0.8  # 20% penalty

            return {
                'passed': True,
                'reason': 'Balanced-strict filters passed',
                'indicators': indicators
            }

        except Exception as e:
            print_colored(f"❌ Error in balanced-strict filters: {str(e)}", "ERROR")
            return {
                'passed': False,
                'reason': f'Filter error: {str(e)}',
                'indicators': {}
            }

    def check_volatility_filter(self, df, config):
        """Check volatility conditions for stability"""
        try:
            volatility_config = config.get('volatility_filters', {})
            max_volatility = volatility_config.get('max_volatility_threshold', 0.002)
            min_stability_candles = volatility_config.get('min_stability_candles', 5)

            # Calculate recent volatility
            recent_candles = df.tail(min_stability_candles)
            price_changes = recent_candles['close'].pct_change().abs()
            avg_volatility = price_changes.mean()

            if avg_volatility > max_volatility:
                return {
                    'passed': False,
                    'reason': f'High volatility detected ({avg_volatility:.6f} > {max_volatility:.6f}) - avoiding unstable conditions',
                    'indicators': {}
                }

            return {
                'passed': True,
                'reason': 'Volatility check passed'
            }

        except Exception as e:
            return {
                'passed': False,
                'reason': f'Volatility filter error: {str(e)}'
            }

    def apply_direction_filters(self, df, expected_direction, filter_result):
        """
        Apply BALANCED-STRICT direction-specific filters for 70%+ win rate
        """
        try:
            config = self.echo_sniper_config.get('advanced_filters', {})
            direction_config = config.get('direction_filters', {})
            indicators = filter_result.get('indicators', {})
            current_rsi = indicators.get('rsi', 50)
            quality_score = indicators.get('quality_score', 0.5)

            # Convert expected direction to signal type
            signal_type = 'put' if expected_direction == 'red' else 'call'

            # Direction-specific RSI optimization
            if signal_type == 'put':
                put_config = direction_config.get('put_rsi_optimization', {})
                preferred_range = put_config.get('preferred_range', [10, 50])
                avoid_range = put_config.get('avoid_range', [50, 100])

                # Check if in preferred range
                if not (preferred_range[0] <= current_rsi <= preferred_range[1]):
                    return {
                        'passed': False,
                        'reason': f'PUT signal RSI {current_rsi:.1f} outside preferred range {preferred_range[0]}-{preferred_range[1]}'
                    }

                # Reject if in avoid range (should already be caught by main RSI filter)
                if avoid_range[0] <= current_rsi <= avoid_range[1]:
                    return {
                        'passed': False,
                        'reason': f'PUT signal RSI {current_rsi:.1f} in avoid range {avoid_range[0]}-{avoid_range[1]}'
                    }

            # CALL signal filtering (more restrictive since CALL performs worse)
            elif signal_type == 'call':
                call_config = direction_config.get('call_rsi_optimization', {})
                preferred_range = call_config.get('preferred_range', [30, 50])
                avoid_ranges = call_config.get('avoid_ranges', [[0, 30], [50, 100]])

                # Must be in preferred range
                if not (preferred_range[0] <= current_rsi <= preferred_range[1]):
                    return {
                        'passed': False,
                        'reason': f'CALL signal RSI {current_rsi:.1f} outside preferred range {preferred_range[0]}-{preferred_range[1]}'
                    }

                # Must not be in any avoid range
                for avoid_min, avoid_max in avoid_ranges:
                    if avoid_min <= current_rsi <= avoid_max:
                        return {
                            'passed': False,
                            'reason': f'CALL signal RSI {current_rsi:.1f} in avoid range {avoid_min}-{avoid_max}'
                        }

                # Additional penalty for CALL signals (they perform worse)
                indicators['quality_score'] = quality_score * 0.9  # 10% penalty for CALL

            return {
                'passed': True,
                'reason': f'{signal_type.upper()} signal passed balanced-strict direction filters',
                'indicators': indicators
            }

        except Exception as e:
            print_colored(f"❌ Error in balanced-strict direction filters: {str(e)}", "ERROR")
            return {
                'passed': False,
                'reason': f'Direction filter error: {str(e)}'
            }

    def calculate_enhanced_confidence_score(self, win_rate, total_matches, filter_result, direction_filter_result):
        """
        Calculate BALANCED-STRICT enhanced confidence score for 70%+ win rate
        """
        try:
            config = self.echo_sniper_config.get('advanced_filters', {})
            confidence_config = config.get('confidence_filters', {})
            quality_boosts = config.get('quality_boosts', {})

            # Balanced requirements (not ultra-strict)
            min_occurrences = confidence_config.get('minimum_pattern_occurrences', 3)
            min_confidence = confidence_config.get('minimum_confidence_score', 0.70)
            prefer_win_rate = confidence_config.get('prefer_high_win_rate', 0.65)

            # Check minimum occurrences
            if total_matches < min_occurrences:
                return 'none', 0.0

            # Start with base confidence score
            base_level, base_score = self.calculate_confidence_score(win_rate, total_matches)

            if base_level == 'none':
                return 'none', 0.0

            # Get quality indicators
            indicators = filter_result.get('indicators', {})
            direction_indicators = direction_filter_result.get('indicators', indicators)

            current_rsi = indicators.get('rsi', 50)
            quality_score = direction_indicators.get('quality_score', indicators.get('quality_score', 0.5))
            macd_strength = indicators.get('macd_strength', 0)
            macd_bearish = indicators.get('macd_bearish', False)

            # Calculate enhanced score with quality-based boosts
            enhanced_score = base_score

            # RSI quality boost
            if 30 <= current_rsi <= 50:  # Best documented range
                rsi_boost = quality_boosts.get('rsi_30_50_boost', 0.10)
                enhanced_score += rsi_boost
                print_colored(f"🎯 RSI Quality Boost: +{rsi_boost:.2f} (RSI {current_rsi:.1f} in optimal range)", "SUCCESS")

            # Strong bearish MACD boost
            if macd_bearish and macd_strength > 0.01:
                macd_boost = quality_boosts.get('strong_bearish_macd_boost', 0.05)
                enhanced_score += macd_boost
                print_colored(f"📈 MACD Strength Boost: +{macd_boost:.2f} (strength {macd_strength:.6f})", "SUCCESS")

            # High pattern count boost
            if total_matches >= 10:
                pattern_boost = quality_boosts.get('high_pattern_count_boost', 0.05)
                enhanced_score += pattern_boost
                print_colored(f"📊 Pattern Count Boost: +{pattern_boost:.2f} ({total_matches} occurrences)", "SUCCESS")

            # Win rate bonus
            if win_rate >= 0.75:
                win_rate_bonus = 0.08  # 8% bonus for 75%+ win rate
                enhanced_score += win_rate_bonus
                print_colored(f"🏆 High Win Rate Bonus: +{win_rate_bonus:.2f} ({win_rate:.1%} win rate)", "SUCCESS")
            elif win_rate >= prefer_win_rate:
                win_rate_bonus = 0.04  # 4% bonus for good win rate
                enhanced_score += win_rate_bonus

            # Apply quality score multiplier
            enhanced_score *= (0.8 + quality_score * 0.2)  # Scale between 0.8 and 1.0

            # Cap the score
            enhanced_score = min(0.95, enhanced_score)

            # Check final minimum confidence
            if enhanced_score < min_confidence:
                return 'none', 0.0

            # Determine enhanced level
            if enhanced_score >= 0.85:
                enhanced_level = 'high'
            elif enhanced_score >= 0.75:
                enhanced_level = 'medium'
            elif enhanced_score >= 0.70:
                enhanced_level = 'acceptable'
            else:
                enhanced_level = 'none'
                enhanced_score = 0.0

            return enhanced_level, enhanced_score

        except Exception as e:
            print_colored(f"❌ Error in balanced-strict confidence calculation: {str(e)}", "ERROR")
            return 'none', 0.0

    def final_signal_validation(self, df, signal, confidence_score, win_rate):
        """
        Final validation before signal generation
        """
        try:
            # Minimum confidence threshold
            min_confidence = TRADING_CONFIG.get('MIN_CONFIDENCE', 0.75)
            if confidence_score < min_confidence:
                return {
                    'passed': False,
                    'reason': f'Confidence {confidence_score:.3f} below minimum {min_confidence:.3f}'
                }

            # Minimum win rate validation
            target_win_rate = TRADING_CONFIG.get('TARGET_WIN_RATE', 0.70)
            if win_rate < target_win_rate:
                return {
                    'passed': False,
                    'reason': f'Win rate {win_rate:.1%} below target {target_win_rate:.1%}'
                }

            return {
                'passed': True,
                'reason': 'All final validations passed'
            }

        except Exception as e:
            print_colored(f"❌ Error in final validation: {str(e)}", "ERROR")
            return {
                'passed': False,
                'reason': f'Validation error: {str(e)}'
            }

    def evaluate_echo_sniper_strategy(self, df):
        """
        ENHANCED Echo Sniper Strategy: Pattern-based trading with advanced filtering for 70% win rate

        NEW FEATURES:
        - RSI-based filtering to avoid 50-70 range (73.7% loss rate)
        - MACD trend confirmation (prefer bearish MACD)
        - Direction optimization (prefer PUT signals)
        - Stricter confidence thresholds
        - Multi-layer filtering system

        Steps:
        1. Fetch market data at candle close
        2. Apply advanced technical indicator filters FIRST
        3. Detect pattern from last N closed candles (excluding current running candle)
        4. Search historical data for same pattern occurrences
        5. Calculate win rate based on next candle outcomes after pattern
        6. Apply enhanced confidence scoring with stricter thresholds
        7. Generate signal only if ALL filters pass
        """
        try:
            config = self.echo_sniper_config
            pattern_length = config['pattern_length']
            historical_candles = config['historical_candles']
            min_win_rate = config.get('min_win_rate', 0.70)  # Default to 70%

            # Minimum data requirement: pattern + historical + current candle
            min_required = pattern_length + historical_candles + 1
            if len(df) < min_required:
                print_colored(f"⚠️ Echo Sniper: Insufficient data ({len(df)} < {min_required} candles)", "WARNING")
                return 0, 0.0

            # NEW STEP 1: Apply advanced technical indicator filters BEFORE pattern analysis
            filter_result = self.apply_advanced_filters(df)
            if not filter_result['passed']:
                print_colored(f"🚫 Advanced Filter Rejected: {filter_result['reason']}", "WARNING")
                return 0, 0.0

            # Step 2: Detect current pattern from closed candles (exclude running candle)
            pattern, error = self.detect_pattern(df, pattern_length)
            if error:
                return 0, 0.0

            # Step 3: Find historical occurrences of this exact pattern
            matches, error = self.find_historical_patterns(df, pattern, historical_candles)
            if error or not matches:
                return 0, 0.0

            # Step 4: Calculate pattern statistics and expected direction
            win_rate, total_matches, green_wins, red_wins, expected_direction = self.calculate_pattern_statistics(matches)

            # Step 5: Apply direction-specific filtering
            direction_filter_result = self.apply_direction_filters(df, expected_direction, filter_result)
            if not direction_filter_result['passed']:
                print_colored(f"🚫 Direction Filter Rejected: {direction_filter_result['reason']}", "WARNING")
                return 0, 0.0

            # Step 6: Check ENHANCED minimum win rate threshold
            if win_rate < min_win_rate:
                print_colored(f"🎯 Echo Sniper: Win rate {win_rate:.1%} below STRICT threshold {min_win_rate:.1%}", "WARNING")
                return 0, 0.0

            if not expected_direction:
                return 0, 0.0

            # Step 7: Check if pattern already completed (current candle moved as expected)
            completed, _ = self.check_pattern_completion(df, expected_direction)
            if completed:
                return 0, 0.0

            # Step 8: Calculate ENHANCED confidence score with stricter requirements
            confidence_level, confidence_score = self.calculate_enhanced_confidence_score(
                win_rate, total_matches, filter_result, direction_filter_result
            )

            if confidence_level == 'none':
                return 0, 0.0

            # Step 9: Generate trading signal with final validation
            if expected_direction == 'green':
                signal = 1  # CALL signal
            else:  # expected_direction == 'red'
                signal = -1  # PUT signal

            # Final validation: Ensure signal meets all criteria
            final_validation = self.final_signal_validation(df, signal, confidence_score, win_rate)
            if not final_validation['passed']:
                print_colored(f"🚫 Final Validation Failed: {final_validation['reason']}", "WARNING")
                return 0, 0.0

            print_colored(f"✅ HIGH-QUALITY SIGNAL: {expected_direction.upper()} | Win Rate: {win_rate:.1%} | Confidence: {confidence_score:.3f}", "SUCCESS")
            return signal, confidence_score

        except Exception as e:
            print_colored(f"❌ Error in Enhanced Echo Sniper Strategy: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_all_strategies(self, df, selected_strategies=None):
        """Evaluate ENHANCED Echo Sniper strategy with strict filtering for 70% win rate"""
        # Enhanced Echo Sniper is the only strategy now
        signal, confidence = self.evaluate_echo_sniper_strategy(df)

        # Apply STRICT confidence threshold for high accuracy
        min_confidence = TRADING_CONFIG.get('MIN_CONFIDENCE', 0.75)  # Raised from 0.5 to 0.75

        # Return result in expected format
        if signal != 0 and confidence >= min_confidence:
            signal_name = 'BUY' if signal == 1 else 'SELL'
            print_colored(f"🎯 HIGH-QUALITY SIGNAL GENERATED: {signal_name} | Confidence: {confidence:.3f}", "SUCCESS")
            return {
                'signal': signal_name,
                'confidence': confidence,
                'strategy': 'ENHANCED_ECHO_SNIPER',
                'price': df.iloc[-1]['close'],
                'all_signals': {'ENHANCED_ECHO_SNIPER': {'signal': signal, 'confidence': confidence}}
            }
        else:
            # More detailed rejection logging
            if signal != 0:
                print_colored(f"🚫 Signal rejected: Confidence {confidence:.3f} below threshold {min_confidence:.3f}", "WARNING")
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'strategy': None,
                'price': df.iloc[-1]['close'],
                'all_signals': {'ENHANCED_ECHO_SNIPER': {'signal': signal, 'confidence': confidence}}
            }

    def calculate_atr(self, df, period=14):
        """Calculate Average True Range (utility method)"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        return tr.rolling(period).mean()

    def calculate_rsi_custom(self, df, period=14):
        """Calculate RSI if not available (utility method)"""
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        avg_gain = gain.rolling(period).mean()
        avg_loss = loss.rolling(period).mean()

        rs = avg_gain / avg_loss
        return 100 - (100 / (1 + rs))

    def update_echo_sniper_config(self, new_config):
        """Update Echo Sniper configuration"""
        self.echo_sniper_config.update(new_config)
        print_colored(f"🔧 Echo Sniper config updated: {new_config}", "SUCCESS")

    def get_pattern_info(self, df):
        """Get current pattern information for logging"""
        try:
            pattern, error = self.detect_pattern(df, self.echo_sniper_config['pattern_length'])
            if error:
                return None

            matches, error = self.find_historical_patterns(df, pattern, self.echo_sniper_config['historical_candles'])
            if error:
                return None

            win_rate, total_matches, green_wins, red_wins, expected_direction = self.calculate_pattern_statistics(matches)

            return {
                'pattern': pattern,
                'total_matches': total_matches,
                'green_wins': green_wins,
                'red_wins': red_wins,
                'win_rate': win_rate,
                'expected_direction': expected_direction
            }
        except Exception as e:
            print_colored(f"❌ Error getting pattern info: {str(e)}", "ERROR")
            return None
