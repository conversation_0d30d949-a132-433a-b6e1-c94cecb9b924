#!/usr/bin/env python3
"""
Test Ultra-Strict Filters for 70%+ Win Rate
"""

import json
import pandas as pd
import numpy as np

def test_ultra_strict_filters():
    """Test the ultra-strict filtering system"""
    
    print("="*80)
    print("🔥 TESTING ULTRA-STRICT FILTERS FOR 70%+ WIN RATE")
    print("="*80)
    
    # Load historical signals
    dates = [20, 25, 26]
    all_signals = []
    
    for date in dates:
        try:
            filename = f"signals/signals_2025-07-{date:02d}.json"
            with open(filename, 'r') as f:
                signals = json.load(f)
                all_signals.extend(signals)
        except Exception as e:
            print(f"❌ Error loading signals_2025-07-{date:02d}.json: {e}")
    
    if not all_signals:
        print("❌ No signals loaded!")
        return
    
    print(f"📊 Loaded {len(all_signals)} historical signals")
    
    # Separate wins and losses
    wins = [s for s in all_signals if s['result'] == 'win']
    losses = [s for s in all_signals if s['result'] == 'loss']
    original_win_rate = len(wins) / len(all_signals) * 100
    
    print(f"   Original: {len(wins)}W/{len(losses)}L = {original_win_rate:.1f}% win rate")
    
    # Apply ULTRA-STRICT filters
    print(f"\n🔥 APPLYING ULTRA-STRICT FILTERS:")
    print("-" * 60)
    
    ultra_filtered_signals = []
    rejection_stats = {
        'rsi_not_optimal': 0,
        'macd_not_bearish': 0,
        'macd_too_weak': 0,
        'put_rsi_forbidden': 0,
        'call_rsi_forbidden': 0,
        'high_volatility': 0,
        'low_confidence': 0,
        'insufficient_data': 0
    }
    
    for signal in all_signals:
        indicators = signal.get('indicators', {})
        rsi = indicators.get('rsi', 50)
        macd = indicators.get('macd', 0)
        macd_signal = indicators.get('macd_signal', 0)
        direction = signal.get('direction', 'put')
        confidence = signal.get('confidence_score', 0)
        pattern_count = signal.get('pattern_count', 0)
        pattern_win_rate = signal.get('pattern_win_rate', 0)
        
        # Track rejection reasons
        rejected = False
        rejection_reasons = []
        
        # Ultra-strict RSI filter: Only 30-40 range allowed
        if not (30 <= rsi <= 40):
            rejected = True
            rejection_reasons.append('rsi_not_optimal')
            rejection_stats['rsi_not_optimal'] += 1
        
        # Ultra-strict MACD filter: Must be bearish
        if macd >= macd_signal:
            rejected = True
            rejection_reasons.append('macd_not_bearish')
            rejection_stats['macd_not_bearish'] += 1
        
        # MACD strength filter: Must be strong signal
        macd_strength = abs(macd - macd_signal)
        if macd_strength < 0.01:  # Weak signal threshold
            rejected = True
            rejection_reasons.append('macd_too_weak')
            rejection_stats['macd_too_weak'] += 1
        
        # Direction-specific ultra-strict filters
        if direction == 'put':
            # PUT signals: RSI must be 30-40, forbidden above 40
            if rsi > 40:
                rejected = True
                rejection_reasons.append('put_rsi_forbidden')
                rejection_stats['put_rsi_forbidden'] += 1
        elif direction == 'call':
            # CALL signals: RSI must be 35-45, very restrictive
            if not (35 <= rsi <= 45):
                rejected = True
                rejection_reasons.append('call_rsi_forbidden')
                rejection_stats['call_rsi_forbidden'] += 1
        
        # Pattern confidence filters
        if pattern_count < 10:  # Need at least 10 historical occurrences
            rejected = True
            rejection_reasons.append('insufficient_data')
            rejection_stats['insufficient_data'] += 1
        
        if pattern_win_rate < 0.75:  # Need at least 75% historical win rate
            rejected = True
            rejection_reasons.append('low_confidence')
            rejection_stats['low_confidence'] += 1
        
        # If signal passes all ultra-strict filters
        if not rejected:
            ultra_filtered_signals.append(signal)
    
    # Calculate ultra-strict results
    ultra_wins = len([s for s in ultra_filtered_signals if s['result'] == 'win'])
    ultra_losses = len([s for s in ultra_filtered_signals if s['result'] == 'loss'])
    ultra_total = len(ultra_filtered_signals)
    
    if ultra_total > 0:
        ultra_win_rate = ultra_wins / ultra_total * 100
    else:
        ultra_win_rate = 0
    
    # Display results
    print(f"\n📊 ULTRA-STRICT FILTER RESULTS:")
    print("-" * 60)
    print(f"   Signals remaining: {ultra_total}/{len(all_signals)} ({ultra_total/len(all_signals)*100:.1f}%)")
    print(f"   Ultra-strict win rate: {ultra_win_rate:.1f}% ({ultra_wins}W/{ultra_losses}L)")
    print(f"   Win rate improvement: {ultra_win_rate - original_win_rate:+.1f} percentage points")
    
    # Rejection analysis
    print(f"\n🚫 REJECTION BREAKDOWN:")
    print("-" * 60)
    total_rejections = len(all_signals) - ultra_total
    
    for reason, count in sorted(rejection_stats.items(), key=lambda x: x[1], reverse=True):
        if count > 0:
            percentage = count / len(all_signals) * 100
            reason_display = reason.replace('_', ' ').title()
            print(f"   {reason_display}: {count} signals ({percentage:.1f}%)")
    
    # Quality analysis by RSI ranges
    print(f"\n📈 RSI RANGE ANALYSIS (Ultra-Strict vs Original):")
    print("-" * 60)
    
    rsi_ranges = [
        (0, 30, "Below 30 (Oversold)"),
        (30, 40, "30-40 (OPTIMAL)"),
        (40, 50, "40-50 (Neutral-Low)"),
        (50, 70, "50-70 (DANGEROUS)"),
        (70, 100, "Above 70 (Overbought)")
    ]
    
    for min_rsi, max_rsi, label in rsi_ranges:
        # Original signals in this range
        orig_range_signals = [s for s in all_signals 
                             if min_rsi <= s.get('indicators', {}).get('rsi', 50) < max_rsi]
        orig_range_wins = len([s for s in orig_range_signals if s['result'] == 'win'])
        orig_range_total = len(orig_range_signals)
        orig_range_win_rate = orig_range_wins / orig_range_total * 100 if orig_range_total > 0 else 0
        
        # Ultra-filtered signals in this range
        ultra_range_signals = [s for s in ultra_filtered_signals 
                              if min_rsi <= s.get('indicators', {}).get('rsi', 50) < max_rsi]
        ultra_range_wins = len([s for s in ultra_range_signals if s['result'] == 'win'])
        ultra_range_total = len(ultra_range_signals)
        ultra_range_win_rate = ultra_range_wins / ultra_range_total * 100 if ultra_range_total > 0 else 0
        
        if orig_range_total > 0:
            print(f"   {label}:")
            print(f"     Original: {orig_range_wins}W/{orig_range_total-orig_range_wins}L = {orig_range_win_rate:.1f}%")
            if ultra_range_total > 0:
                print(f"     Ultra-Strict: {ultra_range_wins}W/{ultra_range_total-ultra_range_wins}L = {ultra_range_win_rate:.1f}%")
            else:
                print(f"     Ultra-Strict: 0 signals (all filtered out)")
    
    # Final assessment
    print(f"\n" + "="*80)
    print("🎯 ULTRA-STRICT FILTER ASSESSMENT")
    print("="*80)
    
    if ultra_win_rate >= 70:
        print(f"🎉 SUCCESS: {ultra_win_rate:.1f}% win rate achieved (≥70% target)!")
        print(f"   Ultra-strict filtering is working!")
        
        if ultra_total < len(all_signals) * 0.05:
            print(f"   ⚠️  Very selective: Only {ultra_total/len(all_signals)*100:.1f}% of signals pass")
            print(f"   This means fewer trading opportunities but higher quality")
        
    elif ultra_win_rate >= 60:
        print(f"📈 CLOSE: {ultra_win_rate:.1f}% win rate (close to 70% target)")
        print(f"   Consider minor adjustments to reach 70%")
        
    elif ultra_win_rate > original_win_rate:
        print(f"📊 IMPROVED: {ultra_win_rate:.1f}% win rate (up from {original_win_rate:.1f}%)")
        print(f"   Filters working but need to be even stricter")
        
    else:
        print(f"❌ INEFFECTIVE: {ultra_win_rate:.1f}% win rate")
        print(f"   Ultra-strict filters may be too restrictive")
    
    # Recommendations
    print(f"\n🔧 RECOMMENDATIONS:")
    
    if ultra_total == 0:
        print(f"   • No signals pass ultra-strict filters - too restrictive")
        print(f"   • Consider relaxing some requirements slightly")
        
    elif ultra_win_rate < 70:
        print(f"   • To reach 70% win rate:")
        if rejection_stats['rsi_not_optimal'] < len(all_signals) * 0.8:
            print(f"     - RSI filter is working well, keep it strict")
        if rejection_stats['macd_not_bearish'] > 0:
            print(f"     - MACD bearish requirement is effective")
        if rejection_stats['low_confidence'] > 0:
            print(f"     - Pattern confidence filter needs to be even stricter")
        print(f"     - Consider adding time-based filters")
        print(f"     - Consider adding volume confirmation")
        
    elif ultra_win_rate >= 70:
        print(f"   • Excellent results! Strategy is ready for deployment")
        print(f"   • Monitor performance and adjust if market conditions change")
        
        if ultra_total < 10:
            print(f"   • Very few signals generated - consider if this is acceptable")
            print(f"   • May want to slightly relax filters for more opportunities")

if __name__ == "__main__":
    test_ultra_strict_filters()
