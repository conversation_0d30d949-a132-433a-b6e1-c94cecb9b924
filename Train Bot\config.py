#!/usr/bin/env python3
"""
Trading Bot Configuration
"""

# Oanda API Configuration
OANDA_CONFIG = {
    "ACCESS_TOKEN": "69091c014738dc994e79ba405a77eb84-a46c00ab9195105c2f9a66331e92e9c1",
    "ACCOUNT_ID": "101-004-********-001",
    "BASE_URL": "https://api-fxpractice.oanda.com",  # Practice environment
    "ENVIRONMENT": "practice"  # practice or live
}

# Currency pairs to monitor - EXPANDED LIST
CURRENCY_PAIRS = [
    # Major Pairs
    "EUR_USD", "GBP_USD", "USD_JPY", "AUD_USD", "USD_CAD", "USD_CHF",

    # Cross Pairs
    "EUR_GBP", "EUR_JPY", "EUR_CHF", "EUR_AUD", "EUR_CAD", "EUR_NZD",
    "GBP_JPY", "GBP_CHF", "GBP_AUD", "GBP_CAD", "GBP_NZD",
    "AUD_JPY", "AUD_CHF", "AUD_CAD", "AUD_NZD",
    "CAD_JPY", "CAD_CHF", "CHF_JPY",
    "NZD_USD", "NZD_JPY", "NZD_CHF", "NZD_CAD",

    # Commodity Pairs
    "USD_NOK", "USD_SEK", "EUR_NOK", "EUR_SEK",

    # Additional Popular Pairs
    "USD_ZAR", "USD_MXN", "USD_SGD", "USD_HKD"
]

# Trading Configuration - ENHANCED FOR HIGH ACCURACY
TRADING_CONFIG = {
    "CANDLE_INTERVAL": "M1",  # 1-minute candles (default)
    "FETCH_INTERVAL": 58,     # Fetch data 2 seconds before next candle (58 seconds)
    "LOOKBACK_CANDLES": 120,  # Increased for better analysis (was 100)
    "MIN_CONFIDENCE": 0.75,   # RAISED: Minimum confidence threshold (was 0.6)
    "TARGET_WIN_RATE": 0.70,  # Target 70% win rate
    "STRICT_FILTERING": True, # Enable strict filtering for high accuracy
}

# Timeframe Configuration for Rule-Based Trading
TIMEFRAME_CONFIG = {
    "1min": {
        "granularity": "M1",
        "interval_seconds": 60,
        "fetch_before_seconds": 2,
        "display_name": "1 Minute",
        "description": "Signals every 1 minute"
    },
    "2min": {
        "granularity": "M2",
        "interval_seconds": 120,
        "fetch_before_seconds": 2,
        "display_name": "2 Minutes",
        "description": "Signals every 2 minutes"
    },
    "5min": {
        "granularity": "M5",
        "interval_seconds": 300,
        "fetch_before_seconds": 2,
        "display_name": "5 Minutes",
        "description": "Signals every 5 minutes"
    },
    "10min": {
        "granularity": "M10",
        "interval_seconds": 600,
        "fetch_before_seconds": 2,
        "display_name": "10 Minutes",
        "description": "Signals every 10 minutes"
    },
    "15min": {
        "granularity": "M15",
        "interval_seconds": 900,
        "fetch_before_seconds": 2,
        "display_name": "15 Minutes",
        "description": "Signals every 15 minutes"
    },
    "30min": {
        "granularity": "M30",
        "interval_seconds": 1800,
        "fetch_before_seconds": 2,
        "display_name": "30 Minutes",
        "description": "Signals every 30 minutes"
    },
    "1hour": {
        "granularity": "H1",
        "interval_seconds": 3600,
        "fetch_before_seconds": 2,
        "display_name": "1 Hour",
        "description": "Signals every 1 hour"
    }
}

# Echo Sniper Strategy Configuration
STRATEGY_CONFIG = {
    "ECHO_SNIPER": {
        "name": "Echo Sniper",
        "description": "Pattern-based strategy using historical candle pattern analysis for high-accuracy signals",
        "accuracy": "Dynamic (based on historical pattern performance)",
        "enabled": True
    }
}

# Echo Sniper Configuration Settings - ENHANCED FOR 70% WIN RATE
ECHO_SNIPER_CONFIG = {
    "pattern_length": 4,  # Default: 4 candles make up the pattern (user configurable)
    "historical_candles": 70,  # Default: analyze last 70 candles for pattern occurrences (10-300)
    "fetch_candles": 200,  # Default: fetch 200 candles to test pattern accuracy (20-500)
    "min_win_rate": 0.70,  # STRICT: 70% minimum win rate threshold
    "confidence_thresholds": {
        "high": {"min_win_rate": 0.80, "min_occurrences": 8},  # High confidence: >80% win rate, >8 occurrences
        "medium": {"min_win_rate": 0.75, "min_occurrences": 5},  # Medium confidence: >75% win rate, >5 occurrences
        "low": {"min_win_rate": 0.70, "min_occurrences": 3}  # Low confidence: >70% win rate, >3 occurrences
    },
    # PRECISION-FOCUSED: Target only highest-quality signals for 70%+ win rate
    "advanced_filters": {
        "enable_rsi_filter": True,
        "enable_macd_filter": True,
        "enable_direction_filter": True,
        "enable_precision_mode": True,  # Focus on precision over volume
        "rsi_filters": {
            "absolute_avoid_ranges": [
                [50, 70],   # DEADLY: 73.7% loss rate - NEVER trade
                [0, 10],    # Too volatile and unpredictable
                [70, 100]   # Overbought - high reversal risk
            ],
            "precision_target_range": [30, 50],  # ONLY trade this range (64.7% win rate)
            "allow_secondary_range": False,      # Don't allow 10-30 range (only 49.4%)
        },
        "macd_filters": {
            "require_bearish_macd": True,        # REQUIRE bearish MACD
            "minimum_macd_strength": 0.005,     # Require meaningful MACD signal
            "reject_any_bullish": True,          # Reject ANY bullish MACD
        },
        "direction_filters": {
            "strongly_prefer_put": True,         # Heavily favor PUT signals
            "put_only_mode": True,               # ONLY allow PUT signals (49.1% vs 45.2%)
            "put_rsi_requirements": {
                "strict_range": [30, 50],        # ONLY allow RSI 30-50 for PUT
                "no_exceptions": True            # No exceptions to this rule
            }
        },
        "pattern_filters": {
            "minimum_occurrences": 5,           # Need solid historical evidence
            "minimum_win_rate": 0.70,           # REQUIRE 70%+ historical win rate
            "minimum_confidence": 0.80,         # High confidence requirement
        },
        "quality_requirements": {
            "require_multiple_confirmations": True,  # Need multiple positive signals
            "confirmation_score_threshold": 0.85,   # High confirmation threshold
        }
    }
}



# Display Configuration
DISPLAY_CONFIG = {
    "COLORS": {
        "BUY": "\033[92m",      # Green
        "SELL": "\033[91m",     # Red
        "HOLD": "\033[93m",     # Yellow
        "INFO": "\033[94m",     # Blue
        "SUCCESS": "\033[92m",  # Green
        "WARNING": "\033[93m",  # Yellow
        "ERROR": "\033[91m",    # Red
        "RESET": "\033[0m",     # Reset
        "BOLD": "\033[1m",      # Bold
        "HEADER": "\033[96m",   # Sky Blue (changed from Magenta)
        "DATE": "\033[96m",     # Cyan
        "TIME": "\033[97m",     # White
        "PAIR": "\033[92m",     # Green (changed from Blue)
        "PRICE": "\033[92m",    # Green (changed from Yellow)
        "CONFIDENCE": "\033[92m", # Green
        "STRATEGY": "\033[92m", # Green (changed from Magenta)
        "NO_SIGNAL": "\033[93m", # Yellow (changed from Dark Gray)
        "DARK_ORANGE": "\033[38;5;208m", # Dark Orange for selection prompts
        "SKY_BLUE": "\033[96m",  # Sky Blue for headers and timing
        "PURPLE": "\033[95m",    # Purple for option 3
        "YELLOW_ORANGE": "\033[38;5;214m", # Yellow-Orange for table headers
        "GREEN_OPTION": "\033[92m", # Green for option 1
        "SIGNAL_FOUND": "\033[92m", # Green when signal found
        "SIGNAL_NOT_FOUND": "\033[93m", # Yellow when signal not found
        "PROCESSING_TIME": "\033[96m", # Sky blue for processing time
        "SCAN_COMPLETED": "\033[92m", # Green for scan completed
        "NEXT_SCAN": "\033[96m", # Sky blue for next scan time
        "STOP_MESSAGE": "\033[93m", # Yellow for stop message
        "THANK_YOU": "\033[92m", # Green for thank you message
        "FINAL_MESSAGE": "\033[96m", # Sky blue for final message
        "PEACH": "\033[38;5;217m", # Peach color for demo mode messages
        "GOLD": "\033[38;5;220m",   # Gold color for password and practice messages
        "CRIMSON": "\033[38;5;196m", # Crimson color for pairs, timeframe, strategy, amount
        "LIGHT_GRAY": "\033[37m",    # Light gray for connection messages
        "TEAL": "\033[38;5;30m",     # Teal color for signal headers
        "ROYAL_PURPLE": "\033[38;5;93m", # Royal purple for trading configuration
        "BURNT_ORANGE": "\033[38;5;208m", # Burnt orange for live option
        "JAM": "\033[38;2;103;3;47m", # Jam color (#67032F) for trading configuration
        "ROSEWOOD": "\033[38;2;158;66;68m", # Rosewood #9E4244 for lists
        "OCEAN": "\033[38;2;1;96;100m", # Ocean #016064 for headings
        "FUN": "\033[38;2;0;120;72m", # Fun #007848 for success (replaces Forest green)
        "DARK_MULBERRY": "\033[38;2;76;1;33m", # Dark mulberry #4C0121 for config
        "TROPICAL_RAINFOREST": "\033[38;2;0;117;94m", # Tropical rainforest #00755E
        "TYRIAN_PURPLE": "\033[38;2;102;2;60m" # Tyrian purple #66023C for options
    },
    "ICONS": {
        "DATE": "📅",
        "TIME": "🕐",
        "PAIR": "💱",
        "PRICE": "💰",
        "BUY": "📈",
        "SELL": "📉",
        "CONFIDENCE": "🎯",
        "STRATEGY": "🔧",
        "NO_SIGNAL": "❌",
        "ML": "🧠",
        "RULE": "📊"
    },
    "TABLE_WIDTH": 80,
    "DECIMAL_PLACES": 5,
    "SIGNAL_TABLE": {
        "COLUMN_WIDTHS": [12, 10, 12, 12, 10, 12, 15],
        "HEADERS": ["Date", "Time", "Pair", "Price", "Signal", "Confidence", "Strategy"],
        "TOTAL_WIDTH": 95
    }
}

# Advanced Signal Generator Configuration
ADVANCED_SIGNAL_CONFIG = {
    "TIMEFRAMES": ["M1", "M5", "M15", "M30", "H1"],
    "TIMEFRAME_NAMES": {
        "M1": "1 Minute",
        "M5": "5 Minutes",
        "M15": "15 Minutes",
        "M30": "30 Minutes",
        "H1": "1 Hour"
    },
    "DEFAULT_ANALYSIS_DAYS": 5,
    "MIN_ANALYSIS_DAYS": 3,
    "MAX_ANALYSIS_DAYS": 10,
    "DEFAULT_START_TIME": "19:00",
    "DEFAULT_END_TIME": "22:00",
    "CANDLE_STRENGTH_THRESHOLD": 0.5,  # Minimum body size as % of candle range
    "MIN_PATTERN_CONFIDENCE": 0.2,  # 20% - more lenient to show more signals
    "SCORING_WEIGHTS": {
        "PATTERN_CONSISTENCY": 60,  # Base score for consistent pattern
        "TREND_CONFIRMATION": 25,   # Trend alignment bonus
        "CANDLE_STRENGTH": 15       # Strong candle body bonus
    },
    "FILTERS": {
        "USE_TREND_FILTER": True,
        "USE_STRUCTURE_FILTER": False,  # Removed to be less strict
        "USE_RSI_FILTER": False,        # Removed to be less strict
        "USE_MACD_FILTER": False,       # Removed to be less strict
        "USE_STRENGTH_FILTER": True
    }
}

# Backtesting Configuration
BACKTEST_CONFIG = {
    "DEFAULT_CANDLES": 1000,
    "MAX_CANDLES": 10000,
    "WIN_THRESHOLD": 0.0001,  # Minimum price movement to consider a win (1 pip for most pairs)
    "RESULTS_PRECISION": 4
}
