import json
import pandas as pd
import numpy as np
from collections import defaultdict

def load_and_analyze_detailed_patterns():
    """Perform detailed pattern analysis to find specific winning/losing conditions"""
    
    # Load data for specified dates
    dates = [20, 25, 26]
    all_signals = []
    
    for date in dates:
        try:
            filename = f"signals/signals_2025-07-{date:02d}.json"
            with open(filename, 'r') as f:
                signals = json.load(f)
                all_signals.extend(signals)
        except Exception as e:
            print(f"Error loading {filename}: {e}")
    
    # Convert to DataFrame
    df_data = []
    for signal in signals:
        row = {
            'date': signal['date'],
            'pair': signal['pair'],
            'direction': signal['direction'],
            'result': signal['result'],
            'price': signal['price'],
            'final_price': signal.get('final_price', signal['price']),
            'strategy': signal['strategy_used'],
            'confidence_score': signal.get('confidence_score', 0),
            'pattern_win_rate': signal.get('pattern_win_rate', 0),
            'pattern_count': signal.get('pattern_count', 0),
        }
        
        # Add indicators
        indicators = signal.get('indicators', {})
        for key, value in indicators.items():
            row[key] = value
            
        df_data.append(row)
    
    df = pd.DataFrame(df_data)
    
    print("="*100)
    print("DETAILED PATTERN ANALYSIS - SPECIFIC CONDITIONS FOR WINS VS LOSSES")
    print("="*100)
    
    wins_df = df[df['result'] == 'win']
    losses_df = df[df['result'] == 'loss']
    
    # Detailed RSI Analysis
    print(f"\n🎯 DETAILED RSI ANALYSIS:")
    print("-" * 60)
    
    rsi_ranges = [
        (0, 10, "Extremely Oversold"),
        (10, 20, "Very Oversold"), 
        (20, 30, "Oversold"),
        (30, 40, "Bearish"),
        (40, 60, "Neutral"),
        (60, 70, "Bullish"),
        (70, 80, "Overbought"),
        (80, 90, "Very Overbought"),
        (90, 100, "Extremely Overbought")
    ]
    
    for min_rsi, max_rsi, label in rsi_ranges:
        win_count = len(wins_df[(wins_df['rsi'] >= min_rsi) & (wins_df['rsi'] < max_rsi)])
        loss_count = len(losses_df[(losses_df['rsi'] >= min_rsi) & (losses_df['rsi'] < max_rsi)])
        total = win_count + loss_count
        
        if total > 0:
            win_rate = win_count / total * 100
            print(f"{label:20} (RSI {min_rsi:2d}-{max_rsi:2d}): {win_count:3d} wins, {loss_count:3d} losses → {win_rate:5.1f}% win rate")
    
    # MACD Detailed Analysis
    print(f"\n📈 DETAILED MACD ANALYSIS:")
    print("-" * 60)
    
    # MACD histogram analysis
    wins_df['macd_histogram'] = wins_df['macd'] - wins_df['macd_signal']
    losses_df['macd_histogram'] = losses_df['macd'] - losses_df['macd_signal']
    
    macd_conditions = [
        ("MACD > Signal (Bullish)", lambda df: df['macd'] > df['macd_signal']),
        ("MACD < Signal (Bearish)", lambda df: df['macd'] <= df['macd_signal']),
        ("MACD Histogram > 0", lambda df: df['macd_histogram'] > 0),
        ("MACD Histogram < 0", lambda df: df['macd_histogram'] <= 0),
        ("Strong Bullish (MACD > Signal + 0.01)", lambda df: df['macd'] > (df['macd_signal'] + 0.01)),
        ("Strong Bearish (MACD < Signal - 0.01)", lambda df: df['macd'] < (df['macd_signal'] - 0.01))
    ]
    
    for condition_name, condition_func in macd_conditions:
        win_count = len(wins_df[condition_func(wins_df)])
        loss_count = len(losses_df[condition_func(losses_df)])
        total = win_count + loss_count
        
        if total > 0:
            win_rate = win_count / total * 100
            print(f"{condition_name:35}: {win_count:3d} wins, {loss_count:3d} losses → {win_rate:5.1f}% win rate")
    
    # EMA Analysis
    print(f"\n📊 EMA TREND ANALYSIS:")
    print("-" * 60)
    
    # EMA trend conditions
    wins_df['ema_trend'] = np.where(wins_df['ema_12'] > wins_df['ema_26'], 'Bullish', 'Bearish')
    losses_df['ema_trend'] = np.where(losses_df['ema_12'] > losses_df['ema_26'], 'Bullish', 'Bearish')
    
    for trend in ['Bullish', 'Bearish']:
        win_count = len(wins_df[wins_df['ema_trend'] == trend])
        loss_count = len(losses_df[losses_df['ema_trend'] == trend])
        total = win_count + loss_count
        
        if total > 0:
            win_rate = win_count / total * 100
            print(f"EMA Trend {trend:8}: {win_count:3d} wins, {loss_count:3d} losses → {win_rate:5.1f}% win rate")
    
    # Price vs SMA Analysis
    print(f"\n💰 PRICE vs SMA ANALYSIS:")
    print("-" * 60)
    
    wins_df['price_vs_sma'] = np.where(wins_df['price'] > wins_df['sma_20'], 'Above SMA', 'Below SMA')
    losses_df['price_vs_sma'] = np.where(losses_df['price'] > losses_df['sma_20'], 'Above SMA', 'Below SMA')
    
    for position in ['Above SMA', 'Below SMA']:
        win_count = len(wins_df[wins_df['price_vs_sma'] == position])
        loss_count = len(losses_df[losses_df['price_vs_sma'] == position])
        total = win_count + loss_count
        
        if total > 0:
            win_rate = win_count / total * 100
            print(f"Price {position:10}: {win_count:3d} wins, {loss_count:3d} losses → {win_rate:5.1f}% win rate")
    
    # Bollinger Bands Analysis
    print(f"\n🎪 BOLLINGER BANDS ANALYSIS:")
    print("-" * 60)
    
    def get_bb_position(row):
        if row['price'] > row['bb_upper']:
            return 'Above Upper Band'
        elif row['price'] < row['bb_lower']:
            return 'Below Lower Band'
        else:
            return 'Within Bands'
    
    wins_df['bb_position'] = wins_df.apply(get_bb_position, axis=1)
    losses_df['bb_position'] = losses_df.apply(get_bb_position, axis=1)
    
    for position in ['Above Upper Band', 'Within Bands', 'Below Lower Band']:
        win_count = len(wins_df[wins_df['bb_position'] == position])
        loss_count = len(losses_df[losses_df['bb_position'] == position])
        total = win_count + loss_count
        
        if total > 0:
            win_rate = win_count / total * 100
            print(f"{position:18}: {win_count:3d} wins, {loss_count:3d} losses → {win_rate:5.1f}% win rate")
    
    # Combined Conditions Analysis
    print(f"\n🔥 HIGH-PROBABILITY WINNING CONDITIONS:")
    print("-" * 60)
    
    # Find combinations that have high win rates
    high_win_conditions = []
    
    # RSI + Direction combinations
    for direction in ['call', 'put']:
        for rsi_min, rsi_max, rsi_label in [(0, 15, "Very Low RSI"), (15, 30, "Low RSI"), (70, 100, "High RSI")]:
            condition_wins = wins_df[(wins_df['direction'] == direction) & 
                                   (wins_df['rsi'] >= rsi_min) & 
                                   (wins_df['rsi'] < rsi_max)]
            condition_losses = losses_df[(losses_df['direction'] == direction) & 
                                       (losses_df['rsi'] >= rsi_min) & 
                                       (losses_df['rsi'] < rsi_max)]
            
            total = len(condition_wins) + len(condition_losses)
            if total >= 10:  # Only consider conditions with sufficient data
                win_rate = len(condition_wins) / total * 100
                if win_rate > 55:  # High win rate threshold
                    high_win_conditions.append({
                        'condition': f"{direction.upper()} + {rsi_label}",
                        'wins': len(condition_wins),
                        'losses': len(condition_losses),
                        'win_rate': win_rate
                    })
    
    # Sort by win rate
    high_win_conditions.sort(key=lambda x: x['win_rate'], reverse=True)
    
    for condition in high_win_conditions[:10]:  # Top 10 conditions
        print(f"{condition['condition']:25}: {condition['wins']:3d} wins, {condition['losses']:3d} losses → {condition['win_rate']:5.1f}% win rate")
    
    print(f"\n🚨 HIGH-PROBABILITY LOSING CONDITIONS:")
    print("-" * 60)
    
    # Find combinations that have high loss rates
    high_loss_conditions = []
    
    for direction in ['call', 'put']:
        for rsi_min, rsi_max, rsi_label in [(0, 15, "Very Low RSI"), (15, 30, "Low RSI"), (70, 100, "High RSI")]:
            condition_wins = wins_df[(wins_df['direction'] == direction) & 
                                   (wins_df['rsi'] >= rsi_min) & 
                                   (wins_df['rsi'] < rsi_max)]
            condition_losses = losses_df[(losses_df['direction'] == direction) & 
                                       (losses_df['rsi'] >= rsi_min) & 
                                       (losses_df['rsi'] < rsi_max)]
            
            total = len(condition_wins) + len(condition_losses)
            if total >= 10:
                loss_rate = len(condition_losses) / total * 100
                if loss_rate > 55:
                    high_loss_conditions.append({
                        'condition': f"{direction.upper()} + {rsi_label}",
                        'wins': len(condition_wins),
                        'losses': len(condition_losses),
                        'loss_rate': loss_rate
                    })
    
    high_loss_conditions.sort(key=lambda x: x['loss_rate'], reverse=True)
    
    for condition in high_loss_conditions[:10]:
        print(f"{condition['condition']:25}: {condition['wins']:3d} wins, {condition['losses']:3d} losses → {condition['loss_rate']:5.1f}% loss rate")

if __name__ == "__main__":
    load_and_analyze_detailed_patterns()
