#!/usr/bin/env python3
"""
Test Precision-Focused Filters for 70%+ Win Rate
Focus on quality over quantity
"""

import json
import pandas as pd
import numpy as np

def test_precision_focused_filters():
    """Test the precision-focused filtering system"""
    
    print("="*80)
    print("🎯 TESTING PRECISION-FOCUSED FILTERS FOR 70%+ WIN RATE")
    print("="*80)
    
    # Load historical signals
    dates = [20, 25, 26]
    all_signals = []
    
    for date in dates:
        try:
            filename = f"signals/signals_2025-07-{date:02d}.json"
            with open(filename, 'r') as f:
                signals = json.load(f)
                all_signals.extend(signals)
        except Exception as e:
            print(f"❌ Error loading signals_2025-07-{date:02d}.json: {e}")
    
    if not all_signals:
        print("❌ No signals loaded!")
        return
    
    print(f"📊 Loaded {len(all_signals)} historical signals")
    
    # Separate wins and losses
    wins = [s for s in all_signals if s['result'] == 'win']
    losses = [s for s in all_signals if s['result'] == 'loss']
    original_win_rate = len(wins) / len(all_signals) * 100
    
    print(f"   Original: {len(wins)}W/{len(losses)}L = {original_win_rate:.1f}% win rate")
    
    # Apply PRECISION-FOCUSED filters (extremely strict)
    print(f"\n🎯 APPLYING PRECISION-FOCUSED FILTERS:")
    print("-" * 60)
    
    precision_signals = []
    rejection_stats = {
        'not_put_signal': 0,           # Not a PUT signal
        'rsi_not_30_50': 0,           # RSI not in 30-50 range
        'macd_not_bearish': 0,        # MACD not bearish
        'macd_too_weak': 0,           # MACD signal too weak
        'insufficient_occurrences': 0, # Less than 5 historical occurrences
        'low_historical_win_rate': 0, # Historical win rate < 70%
        'low_final_confidence': 0,    # Final confidence < 80%
    }
    
    for signal in all_signals:
        indicators = signal.get('indicators', {})
        rsi = indicators.get('rsi', 50)
        macd = indicators.get('macd', 0)
        macd_signal = indicators.get('macd_signal', 0)
        direction = signal.get('direction', 'put')
        confidence = signal.get('confidence_score', 0)
        pattern_count = signal.get('pattern_count', 0)
        pattern_win_rate = signal.get('pattern_win_rate', 0)
        
        # Track rejection reasons
        rejected = False
        rejection_reasons = []
        
        # PRECISION FILTER 1: ONLY PUT signals
        if direction != 'put':
            rejected = True
            rejection_reasons.append('not_put_signal')
            rejection_stats['not_put_signal'] += 1
        
        # PRECISION FILTER 2: RSI MUST be in 30-50 range (64.7% win rate range)
        if not (30 <= rsi <= 50):
            rejected = True
            rejection_reasons.append('rsi_not_30_50')
            rejection_stats['rsi_not_30_50'] += 1
        
        # PRECISION FILTER 3: MACD MUST be bearish
        if macd >= macd_signal:
            rejected = True
            rejection_reasons.append('macd_not_bearish')
            rejection_stats['macd_not_bearish'] += 1
        
        # PRECISION FILTER 4: MACD signal must be strong enough
        macd_strength = abs(macd - macd_signal)
        if macd_strength < 0.005:  # Require meaningful signal
            rejected = True
            rejection_reasons.append('macd_too_weak')
            rejection_stats['macd_too_weak'] += 1
        
        # PRECISION FILTER 5: Need solid historical evidence
        if pattern_count < 5:
            rejected = True
            rejection_reasons.append('insufficient_occurrences')
            rejection_stats['insufficient_occurrences'] += 1
        
        # PRECISION FILTER 6: Historical win rate must be 70%+
        if pattern_win_rate < 0.70:
            rejected = True
            rejection_reasons.append('low_historical_win_rate')
            rejection_stats['low_historical_win_rate'] += 1
        
        # PRECISION FILTER 7: Calculate enhanced confidence
        if not rejected:
            # Start with base confidence
            enhanced_confidence = confidence
            
            # Apply precision boosts
            if 30 <= rsi <= 40:  # Best sub-range within 30-50
                enhanced_confidence += 0.15  # 15% boost for perfect range
            elif 40 <= rsi <= 50:  # Good sub-range
                enhanced_confidence += 0.10  # 10% boost
            
            if macd_strength > 0.01:  # Strong bearish MACD
                enhanced_confidence += 0.10  # 10% boost for strong signal
            
            if pattern_count >= 10:  # Lots of historical data
                enhanced_confidence += 0.08  # 8% boost for solid evidence
            elif pattern_count >= 7:
                enhanced_confidence += 0.05  # 5% boost for good evidence
            
            if pattern_win_rate >= 0.80:  # Excellent historical performance
                enhanced_confidence += 0.12  # 12% boost for excellent history
            elif pattern_win_rate >= 0.75:  # Good historical performance
                enhanced_confidence += 0.08  # 8% boost for good history
            
            # Cap the confidence
            enhanced_confidence = min(0.98, enhanced_confidence)
            
            # Final confidence check
            if enhanced_confidence < 0.80:
                rejected = True
                rejection_reasons.append('low_final_confidence')
                rejection_stats['low_final_confidence'] += 1
            else:
                # Signal passes all precision filters!
                signal_copy = signal.copy()
                signal_copy['enhanced_confidence'] = enhanced_confidence
                precision_signals.append(signal_copy)
    
    # Calculate precision results
    precision_wins = len([s for s in precision_signals if s['result'] == 'win'])
    precision_losses = len([s for s in precision_signals if s['result'] == 'loss'])
    precision_total = len(precision_signals)
    
    if precision_total > 0:
        precision_win_rate = precision_wins / precision_total * 100
    else:
        precision_win_rate = 0
    
    # Display results
    print(f"\n📊 PRECISION-FOCUSED FILTER RESULTS:")
    print("-" * 60)
    print(f"   Signals remaining: {precision_total}/{len(all_signals)} ({precision_total/len(all_signals)*100:.1f}%)")
    print(f"   Precision win rate: {precision_win_rate:.1f}% ({precision_wins}W/{precision_losses}L)")
    print(f"   Win rate improvement: {precision_win_rate - original_win_rate:+.1f} percentage points")
    
    # Rejection analysis
    print(f"\n🚫 PRECISION REJECTION BREAKDOWN:")
    print("-" * 60)
    
    for reason, count in sorted(rejection_stats.items(), key=lambda x: x[1], reverse=True):
        if count > 0:
            percentage = count / len(all_signals) * 100
            reason_display = reason.replace('_', ' ').title()
            print(f"   {reason_display}: {count} signals ({percentage:.1f}%)")
    
    # Analyze the precision signals in detail
    if precision_signals:
        print(f"\n🔍 DETAILED ANALYSIS OF PRECISION SIGNALS:")
        print("-" * 60)
        
        # Confidence distribution
        high_conf = len([s for s in precision_signals if s.get('enhanced_confidence', 0) >= 0.90])
        med_conf = len([s for s in precision_signals if 0.85 <= s.get('enhanced_confidence', 0) < 0.90])
        acc_conf = len([s for s in precision_signals if 0.80 <= s.get('enhanced_confidence', 0) < 0.85])
        
        print(f"   Ultra-High Confidence (≥90%): {high_conf} signals")
        print(f"   High Confidence (85-90%): {med_conf} signals")
        print(f"   Acceptable Confidence (80-85%): {acc_conf} signals")
        
        # Win rates by confidence level
        if high_conf > 0:
            uhc_signals = [s for s in precision_signals if s.get('enhanced_confidence', 0) >= 0.90]
            uhc_wins = len([s for s in uhc_signals if s['result'] == 'win'])
            uhc_win_rate = uhc_wins / high_conf * 100
            print(f"   Ultra-High Confidence Win Rate: {uhc_win_rate:.1f}%")
        
        # RSI distribution within 30-50 range
        rsi_30_40 = len([s for s in precision_signals if 30 <= s.get('indicators', {}).get('rsi', 50) < 40])
        rsi_40_50 = len([s for s in precision_signals if 40 <= s.get('indicators', {}).get('rsi', 50) <= 50])
        
        print(f"   RSI 30-40 range: {rsi_30_40} signals")
        print(f"   RSI 40-50 range: {rsi_40_50} signals")
        
        # Show some example precision signals
        print(f"\n📋 EXAMPLE PRECISION SIGNALS:")
        print("-" * 60)
        
        for i, signal in enumerate(precision_signals[:5]):  # Show first 5
            indicators = signal.get('indicators', {})
            rsi = indicators.get('rsi', 0)
            macd = indicators.get('macd', 0)
            macd_signal = indicators.get('macd_signal', 0)
            confidence = signal.get('enhanced_confidence', 0)
            result = signal.get('result', 'unknown')
            pattern_win_rate = signal.get('pattern_win_rate', 0)
            pattern_count = signal.get('pattern_count', 0)
            
            print(f"   Signal {i+1}: RSI={rsi:.1f}, MACD={macd:.6f}, Conf={confidence:.3f}, "
                  f"Result={result.upper()}, Pattern={pattern_win_rate:.1%}({pattern_count}x)")
    
    # Final assessment
    print(f"\n" + "="*80)
    print("🎯 PRECISION-FOCUSED FILTER ASSESSMENT")
    print("="*80)
    
    if precision_win_rate >= 70:
        print(f"🎉 SUCCESS: {precision_win_rate:.1f}% win rate achieved (≥70% target)!")
        print(f"   Precision-focused filtering works!")
        
        if precision_total >= 5:
            print(f"   ✅ Sufficient signals: {precision_total} high-quality opportunities")
        else:
            print(f"   ⚠️  Very few signals: Only {precision_total} opportunities")
            print(f"   Consider if this frequency is acceptable for trading")
        
    elif precision_win_rate >= 65:
        print(f"📈 VERY CLOSE: {precision_win_rate:.1f}% win rate (close to 70% target)")
        print(f"   Minor adjustments could reach 70%")
        
    elif precision_win_rate > original_win_rate + 15:
        print(f"📊 SIGNIFICANT IMPROVEMENT: {precision_win_rate:.1f}% win rate")
        print(f"   Up {precision_win_rate - original_win_rate:.1f} points from original")
        
    elif precision_total == 0:
        print(f"❌ TOO RESTRICTIVE: No signals pass precision filters")
        print(f"   Filters are too strict - need to relax some requirements")
        
    else:
        print(f"⚠️  MIXED RESULTS: {precision_win_rate:.1f}% win rate")
        print(f"   Some improvement but not reaching target")
    
    # Final recommendations
    print(f"\n🔧 FINAL RECOMMENDATIONS:")
    
    if precision_win_rate >= 70:
        print(f"   🎯 STRATEGY READY FOR DEPLOYMENT!")
        print(f"   • Expected win rate: {precision_win_rate:.1f}%")
        print(f"   • Signal frequency: {precision_total} per {len(all_signals)} historical signals")
        print(f"   • Focus on PUT signals with RSI 30-50 and bearish MACD")
        print(f"   • Require 70%+ historical pattern win rate")
        print(f"   • Use enhanced confidence scoring")
        
    elif precision_win_rate >= 65:
        print(f"   🔧 MINOR TWEAKS NEEDED:")
        print(f"   • Increase minimum historical win rate to 75%")
        print(f"   • Require stronger MACD signals")
        print(f"   • Add time-of-day filtering")
        
    elif precision_total == 0:
        print(f"   🔧 RELAX SOME FILTERS:")
        print(f"   • Reduce minimum historical win rate to 65%")
        print(f"   • Allow RSI 25-50 range instead of 30-50")
        print(f"   • Reduce minimum pattern occurrences to 3")
        
    else:
        print(f"   🔧 STRATEGY ADJUSTMENTS:")
        print(f"   • Current approach shows promise but needs refinement")
        print(f"   • Consider combining with additional indicators")
        print(f"   • Test with different time periods")
    
    print(f"\n✅ ANALYSIS COMPLETE!")
    print(f"   Best approach: Focus on PUT signals, RSI 30-50, bearish MACD")
    print(f"   Target achieved: {'YES' if precision_win_rate >= 70 else 'CLOSE' if precision_win_rate >= 65 else 'NO'}")

if __name__ == "__main__":
    test_precision_focused_filters()
