#!/usr/bin/env python3
"""
Validate Enhanced Filters Against Historical Data
"""

import json
import pandas as pd
import numpy as np

def validate_filters():
    """Validate the enhanced filters against historical signal data"""
    
    print("="*80)
    print("🔍 VALIDATING ENHANCED FILTERS")
    print("="*80)
    
    # Load historical signals
    dates = [20, 25, 26]
    all_signals = []
    
    for date in dates:
        try:
            filename = f"signals/signals_2025-07-{date:02d}.json"
            with open(filename, 'r') as f:
                signals = json.load(f)
                all_signals.extend(signals)
        except Exception as e:
            print(f"❌ Error loading signals_2025-07-{date:02d}.json: {e}")
    
    if not all_signals:
        print("❌ No signals loaded!")
        return
    
    print(f"📊 Loaded {len(all_signals)} historical signals")
    
    # Separate wins and losses
    wins = [s for s in all_signals if s['result'] == 'win']
    losses = [s for s in all_signals if s['result'] == 'loss']
    
    print(f"   Wins: {len(wins)} ({len(wins)/len(all_signals)*100:.1f}%)")
    print(f"   Losses: {len(losses)} ({len(losses)/len(all_signals)*100:.1f}%)")
    
    # Test RSI Filter (Avoid 50-70 range)
    print(f"\n🎯 RSI FILTER VALIDATION:")
    print("-" * 50)
    
    # Count signals in dangerous RSI range (50-70)
    dangerous_rsi_wins = 0
    dangerous_rsi_losses = 0
    safe_rsi_wins = 0
    safe_rsi_losses = 0
    
    for signal in all_signals:
        rsi = signal.get('indicators', {}).get('rsi', 50)
        
        if 50 <= rsi <= 70:  # Dangerous range
            if signal['result'] == 'win':
                dangerous_rsi_wins += 1
            else:
                dangerous_rsi_losses += 1
        else:  # Safe range
            if signal['result'] == 'win':
                safe_rsi_wins += 1
            else:
                safe_rsi_losses += 1
    
    dangerous_total = dangerous_rsi_wins + dangerous_rsi_losses
    safe_total = safe_rsi_wins + safe_rsi_losses
    
    if dangerous_total > 0:
        dangerous_win_rate = dangerous_rsi_wins / dangerous_total * 100
        print(f"   RSI 50-70 (DANGEROUS): {dangerous_rsi_wins}W/{dangerous_rsi_losses}L = {dangerous_win_rate:.1f}% win rate")
    
    if safe_total > 0:
        safe_win_rate = safe_rsi_wins / safe_total * 100
        print(f"   RSI Outside 50-70 (SAFE): {safe_rsi_wins}W/{safe_rsi_losses}L = {safe_win_rate:.1f}% win rate")
    
    # Calculate filter effectiveness
    signals_filtered = dangerous_total
    filter_effectiveness = (dangerous_rsi_losses - dangerous_rsi_wins) / dangerous_total * 100 if dangerous_total > 0 else 0
    
    print(f"   Filter Impact: Would reject {signals_filtered} signals ({signals_filtered/len(all_signals)*100:.1f}%)")
    print(f"   Filter Effectiveness: {filter_effectiveness:.1f}% of filtered signals were losses")
    
    # Test MACD Filter (Prefer bearish MACD)
    print(f"\n📈 MACD FILTER VALIDATION:")
    print("-" * 50)
    
    bullish_macd_wins = 0
    bullish_macd_losses = 0
    bearish_macd_wins = 0
    bearish_macd_losses = 0
    
    for signal in all_signals:
        indicators = signal.get('indicators', {})
        macd = indicators.get('macd', 0)
        macd_signal = indicators.get('macd_signal', 0)
        
        if macd >= macd_signal:  # Bullish MACD
            if signal['result'] == 'win':
                bullish_macd_wins += 1
            else:
                bullish_macd_losses += 1
        else:  # Bearish MACD
            if signal['result'] == 'win':
                bearish_macd_wins += 1
            else:
                bearish_macd_losses += 1
    
    bullish_total = bullish_macd_wins + bullish_macd_losses
    bearish_total = bearish_macd_wins + bearish_macd_losses
    
    if bullish_total > 0:
        bullish_win_rate = bullish_macd_wins / bullish_total * 100
        print(f"   MACD Bullish (AVOID): {bullish_macd_wins}W/{bullish_macd_losses}L = {bullish_win_rate:.1f}% win rate")
    
    if bearish_total > 0:
        bearish_win_rate = bearish_macd_wins / bearish_total * 100
        print(f"   MACD Bearish (PREFER): {bearish_macd_wins}W/{bearish_macd_losses}L = {bearish_win_rate:.1f}% win rate")
    
    # Test Direction Filter (Prefer PUT signals)
    print(f"\n🎯 DIRECTION FILTER VALIDATION:")
    print("-" * 50)
    
    call_wins = len([s for s in wins if s['direction'] == 'call'])
    call_losses = len([s for s in losses if s['direction'] == 'call'])
    put_wins = len([s for s in wins if s['direction'] == 'put'])
    put_losses = len([s for s in losses if s['direction'] == 'put'])
    
    call_total = call_wins + call_losses
    put_total = put_wins + put_losses
    
    if call_total > 0:
        call_win_rate = call_wins / call_total * 100
        print(f"   CALL signals: {call_wins}W/{call_losses}L = {call_win_rate:.1f}% win rate")
    
    if put_total > 0:
        put_win_rate = put_wins / put_total * 100
        print(f"   PUT signals: {put_wins}W/{put_losses}L = {put_win_rate:.1f}% win rate")
    
    # Combined Filter Analysis
    print(f"\n🔥 COMBINED FILTER ANALYSIS:")
    print("-" * 50)
    
    # Apply all filters and see what remains
    filtered_signals = []
    rejected_signals = []
    
    for signal in all_signals:
        indicators = signal.get('indicators', {})
        rsi = indicators.get('rsi', 50)
        macd = indicators.get('macd', 0)
        macd_signal = indicators.get('macd_signal', 0)
        direction = signal.get('direction', 'put')
        
        # Apply filters
        reject_reasons = []
        
        # RSI filter
        if 50 <= rsi <= 70:
            reject_reasons.append("RSI_50_70")
        
        # MACD filter
        if macd >= macd_signal:
            reject_reasons.append("MACD_BULLISH")
        
        # Direction-specific RSI filter
        if direction == 'put' and rsi >= 50:
            reject_reasons.append("PUT_HIGH_RSI")
        elif direction == 'call' and not (30 <= rsi <= 50):
            reject_reasons.append("CALL_BAD_RSI")
        
        if reject_reasons:
            rejected_signals.append({
                'signal': signal,
                'reasons': reject_reasons
            })
        else:
            filtered_signals.append(signal)
    
    # Calculate results after filtering
    filtered_wins = len([s for s in filtered_signals if s['result'] == 'win'])
    filtered_losses = len([s for s in filtered_signals if s['result'] == 'loss'])
    filtered_total = len(filtered_signals)
    
    if filtered_total > 0:
        filtered_win_rate = filtered_wins / filtered_total * 100
    else:
        filtered_win_rate = 0
    
    original_win_rate = len(wins) / len(all_signals) * 100
    
    print(f"   Original signals: {len(all_signals)} ({original_win_rate:.1f}% win rate)")
    print(f"   After filtering: {filtered_total} ({filtered_win_rate:.1f}% win rate)")
    print(f"   Signals rejected: {len(rejected_signals)} ({len(rejected_signals)/len(all_signals)*100:.1f}%)")
    print(f"   Win rate improvement: {filtered_win_rate - original_win_rate:+.1f} percentage points")
    
    # Rejection reason analysis
    print(f"\n📊 REJECTION REASONS:")
    rejection_counts = {}
    for rejected in rejected_signals:
        for reason in rejected['reasons']:
            rejection_counts[reason] = rejection_counts.get(reason, 0) + 1
    
    for reason, count in sorted(rejection_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"   {reason}: {count} signals ({count/len(all_signals)*100:.1f}%)")
    
    # Final assessment
    print(f"\n" + "="*80)
    print("🎯 FILTER EFFECTIVENESS ASSESSMENT")
    print("="*80)
    
    if filtered_win_rate >= 70:
        print(f"✅ EXCELLENT: {filtered_win_rate:.1f}% win rate achieved (≥70% target)!")
        print(f"   Strategy is ready for deployment")
    elif filtered_win_rate >= 60:
        print(f"📈 GOOD: {filtered_win_rate:.1f}% win rate (close to 70% target)")
        print(f"   Consider minor adjustments to reach 70%")
    elif filtered_win_rate > original_win_rate:
        print(f"📊 IMPROVED: {filtered_win_rate:.1f}% win rate (up from {original_win_rate:.1f}%)")
        print(f"   Filters are working but need strengthening")
    else:
        print(f"⚠️  NEEDS WORK: {filtered_win_rate:.1f}% win rate")
        print(f"   Filters may be too restrictive or need adjustment")
    
    print(f"\n🔧 RECOMMENDATIONS:")
    if filtered_total < len(all_signals) * 0.1:
        print(f"   • Very selective strategy ({filtered_total/len(all_signals)*100:.1f}% of signals pass)")
        print(f"   • Good for quality, but may miss opportunities")
    elif filtered_total > len(all_signals) * 0.5:
        print(f"   • Strategy still generates many signals ({filtered_total/len(all_signals)*100:.1f}%)")
        print(f"   • Consider tightening filters further")
    
    if filtered_win_rate < 70:
        print(f"   • To reach 70% win rate, consider:")
        print(f"     - Tightening RSI ranges further")
        print(f"     - Adding volume or volatility filters")
        print(f"     - Requiring higher pattern confidence")
        print(f"     - Adding time-of-day filters")

if __name__ == "__main__":
    validate_filters()
